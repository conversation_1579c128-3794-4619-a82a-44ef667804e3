import { XMLPolicyValidator } from './src/xmlValidator';
import * as path from 'path';
import * as fs from 'fs';

async function testWithExampleFiles() {
  console.log('🧪 Testing AI XML Policy Validator with Example Files\n');
  
  const validator = new XMLPolicyValidator();
  
  try {
    // Initialize the validator
    console.log('📋 Initializing validator...');
    await validator.initialize();
    console.log('✅ Validator initialized\n');
    
    // Train with the original sample
    console.log('🧠 Training with original sample XML...');
    const originalSamplePath = path.join(process.cwd(), 'Main Street America_myprefix_SC3.xml');
    await validator.trainWithSampleXML(originalSamplePath);
    console.log('✅ Training completed\n');
    
    // Test files to validate
    const testFiles = [
      {
        name: 'Valid Policy',
        path: 'test-files/valid-policy.xml',
        description: 'Should pass all validations'
      },
      {
        name: 'Invalid Policy', 
        path: 'test-files/invalid-policy.xml',
        description: 'Should fail multiple validations'
      },
      {
        name: 'Mixed Policy',
        path: 'test-files/mixed-policy.xml', 
        description: 'Should have some valid and some invalid fields'
      }
    ];
    
    // Test each file
    for (let i = 0; i < testFiles.length; i++) {
      const testFile = testFiles[i];
      const filePath = path.join(process.cwd(), testFile.path);
      
      console.log(`\n${'='.repeat(60)}`);
      console.log(`🔍 Test ${i + 1}: ${testFile.name}`);
      console.log(`📄 File: ${testFile.path}`);
      console.log(`📝 Expected: ${testFile.description}`);
      console.log(`${'='.repeat(60)}\n`);
      
      if (!fs.existsSync(filePath)) {
        console.log(`❌ File not found: ${filePath}\n`);
        continue;
      }
      
      try {
        // Validate the file
        const results = await validator.validateXMLFile(filePath);
        
        // Display summary
        console.log('📊 VALIDATION SUMMARY:');
        console.log(`   Overall Status: ${results.isValid ? '✅ VALID' : '❌ INVALID'}`);
        console.log(`   Total Fields: ${results.summary.totalFields}`);
        console.log(`   Valid Fields: ${results.summary.validFields}`);
        console.log(`   Invalid Fields: ${results.summary.invalidFields}`);
        console.log(`   Average Confidence: ${Math.round(results.summary.averageConfidence * 100)}%\n`);
        
        // Display extracted data
        console.log('📄 EXTRACTED POLICY DATA:');
        Object.entries(results.policyData).forEach(([key, value]) => {
          console.log(`   ${key}: ${value || 'N/A'}`);
        });
        console.log();
        
        // Display detailed validation results
        console.log('🔬 DETAILED VALIDATION RESULTS:');
        results.validationResults.forEach((result, index) => {
          const status = result.isValid ? '✅' : '❌';
          const confidence = Math.round(result.confidence * 100);
          console.log(`   ${index + 1}. ${status} ${result.field}`);
          console.log(`      Message: ${result.message}`);
          console.log(`      Confidence: ${confidence}%`);
          console.log();
        });
        
        // Show issues if any
        const issues = results.validationResults.filter(r => !r.isValid);
        if (issues.length > 0) {
          console.log('🚨 VALIDATION ISSUES FOUND:');
          issues.forEach((issue, index) => {
            console.log(`   ${index + 1}. ${issue.field}: ${issue.message}`);
          });
          console.log();
        }
        
      } catch (error) {
        console.error(`❌ Error validating ${testFile.name}:`, error);
      }
    }
    
    // Summary of all tests
    console.log(`\n${'='.repeat(60)}`);
    console.log('📈 TEST SUMMARY');
    console.log(`${'='.repeat(60)}`);
    console.log('✅ All test files processed successfully!');
    console.log('\n🎯 What these tests demonstrate:');
    console.log('   • XML parsing and field extraction');
    console.log('   • AI-powered validation with confidence scores');
    console.log('   • Business rule validation (dates, VIN format, etc.)');
    console.log('   • Detection of invalid data');
    console.log('   • Comprehensive reporting');
    console.log('\n🚀 The AI XML Policy Validator is working correctly!');
    
  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
}

// Function to show what's in each test file
function showTestFileContents() {
  console.log('📋 Test File Descriptions:\n');
  
  const descriptions = [
    {
      file: 'test-files/valid-policy.xml',
      title: '✅ Valid Policy XML',
      details: [
        'Policy Number: POL2024001234 (valid format)',
        'Insured Name: Johnson Michael (valid name)',
        'Effective Date: 2024-02-01 (valid date)',
        'Expiration Date: 2025-02-01 (valid, after effective)',
        'Vehicle VIN: 1HGBH41JXMN109186 (valid 17-char VIN)',
        'Driver License: J123456789012 (valid format)'
      ]
    },
    {
      file: 'test-files/invalid-policy.xml', 
      title: '❌ Invalid Policy XML',
      details: [
        'Policy Number: BAD (too short)',
        'Insured Name: (empty)',
        'Effective Date: not-a-valid-date (invalid format)',
        'Expiration Date: 2020-01-01 (before effective date)',
        'Vehicle VIN: TOOSHORT (not 17 characters)',
        'Driver License: INVALID (invalid format)'
      ]
    },
    {
      file: 'test-files/mixed-policy.xml',
      title: '⚠️ Mixed Policy XML',
      details: [
        'Policy Number: POL2024567890 (valid format)',
        'Insured Name: Smith Jane (valid name)',
        'Effective Date: 2024-03-01 (valid date)',
        'Expiration Date: 2024-01-01 (INVALID - before effective)',
        'Vehicle VIN: 4T1BF1FK5CU123456 (valid 17-char VIN)',
        'Driver License: S987654321098 (valid format)'
      ]
    }
  ];
  
  descriptions.forEach((desc, index) => {
    console.log(`${index + 1}. ${desc.title}`);
    console.log(`   File: ${desc.file}`);
    desc.details.forEach(detail => {
      console.log(`   • ${detail}`);
    });
    console.log();
  });
}

// Main execution
if (require.main === module) {
  console.log('🎯 AI XML Policy Validator - Test Suite\n');
  
  // Show what we're testing
  showTestFileContents();
  
  // Run the tests
  testWithExampleFiles().catch(console.error);
}
