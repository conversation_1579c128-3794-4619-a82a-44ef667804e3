{"name": "ai-xml-policy-validator", "version": "1.0.0", "description": "AI-powered XML policy validator POC using Groq and Qdrant", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "ts-node src/test.ts", "web": "node server.js", "web-dev": "nodemon server.js"}, "keywords": ["xml", "validation", "ai", "groq", "qdrant", "typescript"], "author": "", "license": "MIT", "dependencies": {"@qdrant/js-client-rest": "^1.9.0", "dotenv": "^16.4.5", "express": "^4.21.2", "form-data": "^4.0.4", "groq-sdk": "^0.7.0", "multer": "^1.4.5-lts.1", "openai": "^4.52.7", "uuid": "^9.0.1", "xml2js": "^0.6.2"}, "devDependencies": {"@types/express": "^4.17.23", "@types/multer": "^1.4.13", "@types/node": "^20.14.10", "@types/uuid": "^9.0.8", "@types/xml2js": "^0.4.14", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.5.3"}}