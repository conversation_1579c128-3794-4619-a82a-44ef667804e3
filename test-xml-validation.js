// Test script to check if XML validation uses AI
const fetch = require('node-fetch');
const FormData = require('form-data');
const fs = require('fs');

async function testXMLValidation() {
    console.log('🧪 Testing XML Validation with AI...\n');
    
    try {
        // Create form data with the example XML file
        const form = new FormData();
        const xmlContent = fs.readFileSync('example-policy.xml');
        form.append('xmlFile', xmlContent, 'example-policy.xml');
        
        console.log('📁 Uploading example-policy.xml for validation...');
        
        const response = await fetch('http://localhost:3000/api/validate', {
            method: 'POST',
            body: form
        });
        
        if (response.ok) {
            const result = await response.json();
            
            if (result.success) {
                console.log('✅ Validation completed successfully');
                console.log(`📊 Overall Valid: ${result.data.isValid}`);
                console.log(`📈 Total Fields: ${result.data.summary.totalFields}`);
                console.log(`✅ Valid Fields: ${result.data.summary.validFields}`);
                console.log(`❌ Invalid Fields: ${result.data.summary.invalidFields}`);
                console.log(`🎯 Average Confidence: ${Math.round(result.data.summary.averageConfidence * 100)}%`);
                
                console.log('\n🔍 Field Validation Details:');
                result.data.validationResults.forEach((field, index) => {
                    const status = field.isValid ? '✅' : '❌';
                    const confidence = Math.round(field.confidence * 100);
                    console.log(`${index + 1}. ${status} ${field.field}: ${field.message} (${confidence}% confidence)`);
                });
                
                // Check if confidence scores suggest AI validation
                const avgConfidence = result.data.summary.averageConfidence;
                if (avgConfidence === 0.85 || avgConfidence === 0.90) {
                    console.log('\n💡 ANALYSIS: Using business rules validation (fixed confidence scores)');
                    console.log('   - Business rules give 85% confidence for valid, 90% for invalid');
                    console.log('   - AI validation would show more varied confidence scores');
                } else {
                    console.log('\n🤖 ANALYSIS: Likely using AI validation (varied confidence scores)');
                }
                
            } else {
                console.log('❌ Validation failed:', result.error);
            }
        } else {
            console.log(`❌ HTTP Error: ${response.status}`);
        }
        
    } catch (error) {
        console.log(`❌ Test failed: ${error.message}`);
    }
}

// Run the test
testXMLValidation().catch(console.error);
