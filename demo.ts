import { XMLPolicyValidator } from './src/xmlValidator';
import * as path from 'path';

async function runDemo() {
  console.log('🚀 AI XML Policy Validator Demo\n');
  console.log('This demo will show the key features of the validator:\n');
  
  const validator = new XMLPolicyValidator();
  
  try {
    // Step 1: Initialize
    console.log('📋 Step 1: Initializing the AI XML Policy Validator...');
    await validator.initialize();
    console.log('✅ Validator initialized successfully!\n');
    
    // Step 2: Parse and display sample XML structure
    console.log('📄 Step 2: Analyzing the sample XML file...');
    const sampleXMLPath = path.join(process.cwd(), 'Main Street America_myprefix_SC3.xml');
    
    // Parse the XML to show what we extracted
    const { XMLParser } = await import('./src/xmlParser');
    const parser = new XMLParser();
    const parsedXML = await parser.parseXMLFile(sampleXMLPath);
    const policyData = parser.extractPolicyData(parsedXML);
    
    console.log('📊 Extracted Policy Data:');
    Object.entries(policyData).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    console.log();
    
    // Step 3: Train the system
    console.log('🧠 Step 3: Training the AI system with the sample XML...');
    await validator.trainWithSampleXML(sampleXMLPath);
    console.log('✅ Training completed! The system now understands ACORD XML patterns.\n');
    
    // Step 4: Validate the XML
    console.log('🔍 Step 4: Validating the XML file...');
    const results = await validator.validateXMLFile(sampleXMLPath);
    
    // Display summary
    console.log('📈 Validation Summary:');
    console.log(`   Overall Status: ${results.isValid ? '✅ VALID' : '❌ INVALID'}`);
    console.log(`   Fields Checked: ${results.summary.totalFields}`);
    console.log(`   Valid Fields: ${results.summary.validFields}`);
    console.log(`   Invalid Fields: ${results.summary.invalidFields}`);
    console.log(`   Average AI Confidence: ${Math.round(results.summary.averageConfidence * 100)}%\n`);
    
    // Display detailed results
    console.log('🔬 Detailed Field Analysis:');
    results.validationResults.forEach((result, index) => {
      const status = result.isValid ? '✅' : '❌';
      const confidence = Math.round(result.confidence * 100);
      console.log(`   ${index + 1}. ${status} ${result.field}`);
      console.log(`      Value: ${policyData[result.field.toLowerCase().replace(/\s+/g, '')] || 'N/A'}`);
      console.log(`      AI Assessment: ${result.message}`);
      console.log(`      Confidence: ${confidence}%\n`);
    });
    
    // Step 5: Test with invalid data
    console.log('🧪 Step 5: Testing with intentionally invalid data...');
    const invalidXML = `<ACORD>
      <InsuranceSvcRq>
        <CommlAutoPolicyQuoteInqRq>
          <CommlPolicy>
            <PolicyNumber>BAD</PolicyNumber>
            <ContractTerm>
              <EffectiveDt>not-a-date</EffectiveDt>
              <ExpirationDt>2020-01-01</ExpirationDt>
            </ContractTerm>
          </CommlPolicy>
          <InsuredOrPrincipal>
            <GeneralPartyInfo>
              <NameInfo>
                <CommlName>
                  <CommercialName></CommercialName>
                </CommlName>
              </NameInfo>
            </GeneralPartyInfo>
          </InsuredOrPrincipal>
          <CommlAutoLineBusiness>
            <CommlRateState>
              <CommlVeh>
                <VehIdentificationNumber>TOOSHORT</VehIdentificationNumber>
              </CommlVeh>
            </CommlRateState>
          </CommlAutoLineBusiness>
        </CommlAutoPolicyQuoteInqRq>
      </InsuranceSvcRq>
    </ACORD>`;
    
    const invalidResults = await validator.validateXMLString(invalidXML);
    
    console.log('🚨 Invalid Data Test Results:');
    console.log(`   Status: ${invalidResults.isValid ? '✅ VALID' : '❌ INVALID (as expected)'}`);
    console.log(`   Invalid Fields Detected: ${invalidResults.summary.invalidFields}`);
    
    if (invalidResults.summary.invalidFields > 0) {
      console.log('   Issues Found:');
      invalidResults.validationResults
        .filter(r => !r.isValid)
        .forEach(result => {
          console.log(`     - ${result.field}: ${result.message}`);
        });
    }
    console.log();
    
    // Step 6: Show vector database info
    console.log('💾 Step 6: Vector Database Information:');
    const vectorInfo = await validator.getVectorStoreInfo();
    console.log(`   Collection Name: ${process.env.QDRANT_COLLECTION_NAME || 'xml_policy_embeddings'}`);
    console.log(`   Stored Patterns: ${vectorInfo.points_count || 0} chunks`);
    console.log(`   Vector Dimensions: ${vectorInfo.config?.params?.vectors?.size || 'N/A'}`);
    console.log();
    
    console.log('🎉 Demo completed successfully!');
    console.log('\n📝 Summary of what we demonstrated:');
    console.log('   ✅ XML parsing and field extraction');
    console.log('   ✅ AI-powered validation using Groq');
    console.log('   ✅ Vector database storage with Qdrant');
    console.log('   ✅ Business rule validation');
    console.log('   ✅ Confidence scoring');
    console.log('   ✅ Invalid data detection');
    console.log('\n🚀 The AI XML Policy Validator is ready for use!');
    
  } catch (error) {
    console.error('❌ Demo failed:', error);
    console.log('\n🔧 Troubleshooting tips:');
    console.log('   1. Make sure your .env file has valid API keys');
    console.log('   2. Check that the sample XML file exists');
    console.log('   3. Verify Qdrant and Groq services are accessible');
    console.log('   4. Run "npm install" to ensure all dependencies are installed');
  }
}

// Run the demo
if (require.main === module) {
  runDemo();
}
