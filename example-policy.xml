<?xml version="1.0" encoding="UTF-8"?>
<ACORD>
  <BookTransferMeta>
    <creation>
      <type>xmlExtraction</type>
      <origin>ExampleSystem</origin>
    </creation>
  </BookTransferMeta>
  <SignonRq>
    <SignonPswd>
      <CustId>
        <SPName>ExampleInsurance.com</SPName>
        <CustPermId/>
        <CustLoginId><EMAIL></CustLoginId>
      </CustId>
      <CustPswd>
        <EncryptionTypeCd>NONE</EncryptionTypeCd>
        <Pswd/>
      </CustPswd>
    </SignonPswd>
    <ClientDt>2024-07-28 14:30 PM</ClientDt>
    <CustLangPref>en-US</CustLangPref>
    <ClientApp>
      <Org>Example Insurance Co</Org>
      <n>PolicyValidator</n>
      <Version>V1.0.0</Version>
    </ClientApp>
  </SignonRq>
  <InsuranceSvcRq BypassScrubbing="SkipNaming">
    <RqUID>EXAMPLE-1234-5678-9ABC-DEF012345678</RqUID>
    <CommlAutoPolicyQuoteInqRq>
      <RqUID>POLICY-ABCD-EFGH-1234-************</RqUID>
      <TransactionRequestDt>2024-07-28</TransactionRequestDt>
      <TransactionEffectiveDt>2024-08-01</TransactionEffectiveDt>
      <CurCd>USD</CurCd>
      <Producer>
        <ProducerInfo>
          <ContractNumber>PROD123456</ContractNumber>
          <ProducerRoleCd>Agency</ProducerRoleCd>
        </ProducerInfo>
      </Producer>
      <InsuredOrPrincipal>
        <ItemIdInfo>
          <InsurerId>INS789012</InsurerId>
          <OtherIdentifier>
            <OtherIdTypeCd>Insured</OtherIdTypeCd>
            <OtherId>CUSTOMER001</OtherId>
          </OtherIdentifier>
        </ItemIdInfo>
        <GeneralPartyInfo>
          <NameInfo>
            <PersonName>
              <Surname>Anderson</Surname>
              <GivenName>Sarah</GivenName>
            </PersonName>
            <CommlName>
              <CommercialName>Anderson Sarah</CommercialName>
            </CommlName>
            <LegalEntityCd>CP</LegalEntityCd>
          </NameInfo>
          <Addr>
            <AddrTypeCd>MailingAddress</AddrTypeCd>
            <Addr1>1234 Maple Street</Addr1>
            <City>Portland</City>
            <StateProvCd>OR</StateProvCd>
            <PostalCode>97205</PostalCode>
          </Addr>
          <Communications>
            <PhoneInfo>
              <PhoneTypeCd>Phone</PhoneTypeCd>
              <CommunicationUseCd>Day</CommunicationUseCd>
              <PhoneNumber>******-555-0123</PhoneNumber>
            </PhoneInfo>
          </Communications>
        </GeneralPartyInfo>
        <InsuredOrPrincipalInfo>
          <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
          <BusinessInfo>
            <SICCd>8712</SICCd>
            <NAICSCd>541310</NAICSCd>
            <NumEmployees>2</NumEmployees>
          </BusinessInfo>
        </InsuredOrPrincipalInfo>
      </InsuredOrPrincipal>
      <CommlPolicy id="PolicyLevel_EXAMPLE">
        <PolicyNumber>EXP2024789012</PolicyNumber>
        <LOBCd>AUTOB</LOBCd>
        <LOBSubCd>NPO</LOBSubCd>
        <NAICCd>54321</NAICCd>
        <ControllingStateProvCd>OR</ControllingStateProvCd>
        <ContractTerm>
          <EffectiveDt>2024-08-01</EffectiveDt>
          <ExpirationDt>2025-08-01</ExpirationDt>
          <DurationPeriod>
            <NumUnits>12</NumUnits>
            <UnitMeasurementCd>MON</UnitMeasurementCd>
          </DurationPeriod>
        </ContractTerm>
        <BillingMethodCd>CPB</BillingMethodCd>
        <CurrentTermAmt>
          <Amt>1650.00</Amt>
        </CurrentTermAmt>
        <LanguageCd>E</LanguageCd>
        <RateEffectiveDt>2024-08-01</RateEffectiveDt>
        <PaymentOption>
          <PaymentPlanCd>OT</PaymentPlanCd>
        </PaymentOption>
      </CommlPolicy>
      <Location id="L1_EXAMPLE">
        <ItemIdInfo>
          <AgencyId>0001</AgencyId>
        </ItemIdInfo>
        <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>1234 Maple Street</Addr1>
          <City>Portland</City>
          <StateProvCd>OR</StateProvCd>
          <PostalCode>97205</PostalCode>
        </Addr>
      </Location>
      <CommlAutoLineBusiness>
        <LOBCd>AUTOB</LOBCd>
        <LOBSubCd>NPO</LOBSubCd>
        <NAICCd>54321</NAICCd>
        <CurrentTermAmt>
          <Amt>1650.00</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>1650.00</Amt>
        </NetChangeAmt>
        <RateEffectiveDt>2024-08-01</RateEffectiveDt>
        <CommlRateState>
          <StateProvCd>OR</StateProvCd>
          <CommlVeh LocationRef="L1_EXAMPLE" id="V1_EXAMPLE">
            <ItemIdInfo>
              <InsurerId>0001</InsurerId>
            </ItemIdInfo>
            <Manufacturer>TOYOTA</Manufacturer>
            <Model>PRIUS</Model>
            <ModelYear>2021</ModelYear>
            <VehTypeCd>PP</VehTypeCd>
            <Registration>
              <RegistrationId>ABC123</RegistrationId>
              <StateProvCd>OR</StateProvCd>
            </Registration>
            <CostNewAmt>
              <Amt>32000</Amt>
            </CostNewAmt>
            <FullTermAmt>
              <Amt>1650.00</Amt>
            </FullTermAmt>
            <RegistrationStateProvCd>OR</RegistrationStateProvCd>
            <TerritoryCd>005</TerritoryCd>
            <VehIdentificationNumber>JTDKARFP5M3012345</VehIdentificationNumber>
            <VehSymbolCd>08</VehSymbolCd>
          </CommlVeh>
        </CommlRateState>
        <CommlDriver id="D1_EXAMPLE">
          <ItemIdInfo>
            <InsurerId>0001</InsurerId>
          </ItemIdInfo>
          <GeneralPartyInfo>
            <NameInfo>
              <PersonName>
                <Surname>Anderson</Surname>
                <GivenName>Sarah</GivenName>
              </PersonName>
              <CommlName>
                <CommercialName>Anderson Sarah</CommercialName>
              </CommlName>
            </NameInfo>
          </GeneralPartyInfo>
          <DriverInfo>
            <PersonInfo>
              <GenderCd>F</GenderCd>
              <BirthDt>1988-05-15</BirthDt>
            </PersonInfo>
            <DriversLicense>
              <DriversLicenseNumber>A123456789012</DriversLicenseNumber>
              <StateProvCd>OR</StateProvCd>
            </DriversLicense>
            <License>
              <LicenseTypeCd>Driver</LicenseTypeCd>
              <LicensePermitNumber>A123456789012</LicensePermitNumber>
              <StateProvCd>OR</StateProvCd>
            </License>
          </DriverInfo>
        </CommlDriver>
      </CommlAutoLineBusiness>
    </CommlAutoPolicyQuoteInqRq>
  </InsuranceSvcRq>
</ACORD>
