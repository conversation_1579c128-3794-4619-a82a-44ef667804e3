<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI XML Policy Validator</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-shield-alt"></i>
                    <h1>AI XML Policy Validator</h1>
                </div>
                <p class="subtitle">Intelligent validation for insurance policy XML files</p>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Upload Section -->
            <section class="upload-section">
                <div class="upload-card">
                    <div class="upload-header">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h2>Upload XML File</h2>
                        <p>Drop your ACORD XML file here or click to browse</p>
                    </div>
                    
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <i class="fas fa-file-upload upload-icon"></i>
                            <p class="upload-text">Drag & drop your XML file here</p>
                            <p class="upload-subtext">or</p>
                            <button class="upload-btn" id="uploadBtn">
                                <i class="fas fa-folder-open"></i>
                                Choose File
                            </button>
                            <input type="file" id="fileInput" accept=".xml" hidden>
                        </div>
                    </div>
                    
                    <div class="file-info" id="fileInfo" style="display: none;">
                        <div class="file-details">
                            <i class="fas fa-file-alt"></i>
                            <div class="file-text">
                                <span class="file-name" id="fileName"></span>
                                <span class="file-size" id="fileSize"></span>
                            </div>
                            <button class="remove-file" id="removeFile">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <button class="validate-btn" id="validateBtn">
                            <i class="fas fa-check-circle"></i>
                            Validate XML
                        </button>
                    </div>
                </div>
            </section>

            <!-- Results Section -->
            <section class="results-section" id="resultsSection" style="display: none;">
                <div class="results-card">
                    <div class="results-header">
                        <i class="fas fa-chart-line"></i>
                        <h2>Validation Results</h2>
                    </div>
                    <div class="results-content" id="resultsContent">
                        <!-- Results will be populated here -->
                    </div>
                </div>
            </section>

            <!-- Chatbot Section -->
            <section class="chatbot-section">
                <div class="chatbot-card">
                    <div class="chatbot-header">
                        <div class="chatbot-title">
                            <i class="fas fa-robot"></i>
                            <h2>AI Assistant</h2>
                            <span class="status-indicator online"></span>
                        </div>
                        <button class="minimize-btn" id="minimizeChat">
                            <i class="fas fa-minus"></i>
                        </button>
                    </div>
                    
                    <div class="chatbot-body" id="chatbotBody">
                        <div class="chat-messages" id="chatMessages">
                            <div class="message bot-message">
                                <div class="message-avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="message-content">
                                    <p>Hello! I'm your AI XML validation assistant. Upload an XML file to get started, or ask me any questions about XML policy validation.</p>
                                    <span class="message-time">Just now</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="chat-input-container">
                            <div class="chat-input-wrapper">
                                <input type="text" id="chatInput" placeholder="Ask me about XML validation..." maxlength="500">
                                <button id="sendMessage" class="send-btn">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                            <div class="quick-actions">
                                <button class="quick-action" data-message="What fields do you validate?">
                                    <i class="fas fa-list"></i>
                                    What fields?
                                </button>
                                <button class="quick-action" data-message="How does the validation work?">
                                    <i class="fas fa-cog"></i>
                                    How it works?
                                </button>
                                <button class="quick-action" data-message="Show me validation examples">
                                    <i class="fas fa-eye"></i>
                                    Examples
                                </button>
                                <button class="quick-action" data-message="Hello, can you help me?">
                                    <i class="fas fa-hand-wave"></i>
                                    Hello
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner"></div>
                <p>Validating your XML file...</p>
                <small>This may take a few moments</small>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
