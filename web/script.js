// Global variables
let currentFile = null;
let chatHistory = [];

// DOM elements
const uploadArea = document.getElementById('uploadArea');
const fileInput = document.getElementById('fileInput');
const uploadBtn = document.getElementById('uploadBtn');
const fileInfo = document.getElementById('fileInfo');
const fileName = document.getElementById('fileName');
const fileSize = document.getElementById('fileSize');
const removeFile = document.getElementById('removeFile');
const validateBtn = document.getElementById('validateBtn');
const resultsSection = document.getElementById('resultsSection');
const resultsContent = document.getElementById('resultsContent');
const loadingOverlay = document.getElementById('loadingOverlay');
const chatMessages = document.getElementById('chatMessages');
const chatInput = document.getElementById('chatInput');
const sendMessage = document.getElementById('sendMessage');
const quickActions = document.querySelectorAll('.quick-action');
const minimizeChat = document.getElementById('minimizeChat');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    addBotMessage("Welcome! I'm ready to help you validate XML policy files. Upload a file to get started!");
});

// Event listeners
function initializeEventListeners() {
    // File upload events
    uploadArea.addEventListener('click', () => fileInput.click());
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);
    removeFile.addEventListener('click', clearFile);
    validateBtn.addEventListener('click', validateFile);
    
    // Chat events
    sendMessage.addEventListener('click', handleSendMessage);
    chatInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') handleSendMessage();
    });
    
    // Quick action buttons
    quickActions.forEach(btn => {
        btn.addEventListener('click', () => {
            const message = btn.getAttribute('data-message');
            sendUserMessage(message);
        });
    });
    
    // Minimize chat
    minimizeChat.addEventListener('click', toggleChat);
}

// File handling functions
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFile(files[0]);
    }
}

function handleFileSelect(e) {
    const file = e.target.files[0];
    if (file) {
        handleFile(file);
    }
}

function handleFile(file) {
    if (!file.name.toLowerCase().endsWith('.xml')) {
        addBotMessage("⚠️ Please upload an XML file. I can only validate XML files.");
        return;
    }
    
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
        addBotMessage("⚠️ File is too large. Please upload a file smaller than 10MB.");
        return;
    }
    
    currentFile = file;
    displayFileInfo(file);
    addBotMessage(`📁 Great! I see you've uploaded "${file.name}". Click the validate button to start the analysis.`);
}

function displayFileInfo(file) {
    fileName.textContent = file.name;
    fileSize.textContent = formatFileSize(file.size);
    fileInfo.style.display = 'block';
    uploadArea.style.display = 'none';
}

function clearFile() {
    currentFile = null;
    fileInfo.style.display = 'none';
    uploadArea.style.display = 'block';
    fileInput.value = '';
    resultsSection.style.display = 'none';
    addBotMessage("File removed. You can upload a new XML file whenever you're ready.");
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Validation functions
async function validateFile() {
    if (!currentFile) {
        addBotMessage("❌ No file selected. Please upload an XML file first.");
        return;
    }

    showLoading();
    addBotMessage("🔍 Starting validation process... This may take a moment.");

    try {
        // Create FormData for file upload
        const formData = new FormData();
        formData.append('xmlFile', currentFile);

        // Call the validation API
        const response = await fetch('/api/validate', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            // Display results
            displayResults(result.data);
            addBotMessage("✅ Validation complete! Check the results above. Feel free to ask me any questions about the findings.");
        } else {
            throw new Error(result.error || 'Validation failed');
        }

    } catch (error) {
        console.error('Validation error:', error);
        addBotMessage(`❌ Sorry, there was an error validating your file: ${error.message}. Please try again or upload a different file.`);
    } finally {
        hideLoading();
    }
}

// Removed simulation functions - now using real API

function displayResults(results) {
    const statusClass = results.isValid ? 'success' : 'error';
    const statusIcon = results.isValid ? '✅' : '❌';
    const statusText = results.isValid ? 'VALID' : 'INVALID';
    
    resultsContent.innerHTML = `
        <div class="validation-summary ${statusClass}">
            <div class="summary-header">
                <span class="status-icon">${statusIcon}</span>
                <h3>Overall Status: ${statusText}</h3>
            </div>
            <div class="summary-stats">
                <div class="stat">
                    <span class="stat-number">${results.summary.totalFields}</span>
                    <span class="stat-label">Total Fields</span>
                </div>
                <div class="stat">
                    <span class="stat-number">${results.summary.validFields}</span>
                    <span class="stat-label">Valid</span>
                </div>
                <div class="stat">
                    <span class="stat-number">${results.summary.invalidFields}</span>
                    <span class="stat-label">Invalid</span>
                </div>
                <div class="stat">
                    <span class="stat-number">${Math.round(results.summary.averageConfidence * 100)}%</span>
                    <span class="stat-label">Confidence</span>
                </div>
            </div>
        </div>
        
        <div class="policy-data">
            <h4><i class="fas fa-file-alt"></i> Extracted Policy Data</h4>
            <div class="data-grid">
                ${Object.entries(results.policyData).map(([key, value]) => `
                    <div class="data-item">
                        <span class="data-label">${formatFieldName(key)}:</span>
                        <span class="data-value">${value}</span>
                    </div>
                `).join('')}
            </div>
        </div>
        
        <div class="field-results">
            <h4><i class="fas fa-check-circle"></i> Field Validation Results</h4>
            <div class="results-list">
                ${results.validationResults.map(result => `
                    <div class="result-item ${result.isValid ? 'valid' : 'invalid'}">
                        <div class="result-header">
                            <span class="result-icon">${result.isValid ? '✅' : '❌'}</span>
                            <span class="result-field">${result.field}</span>
                            <span class="result-confidence">${Math.round(result.confidence * 100)}%</span>
                        </div>
                        <div class="result-message">${result.message}</div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
    
    resultsSection.style.display = 'block';
    resultsSection.scrollIntoView({ behavior: 'smooth' });
}

function formatFieldName(key) {
    return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
}

// Chat functions
function handleSendMessage() {
    const message = chatInput.value.trim();
    if (message) {
        sendUserMessage(message);
        chatInput.value = '';
    }
}

async function sendUserMessage(message) {
    addUserMessage(message);
    console.log('User message:', message); // Debug log

    try {
        // Try to use the API for more dynamic responses
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ message })
        });

        if (response.ok) {
            const result = await response.json();
            console.log('API response:', result); // Debug log
            if (result.success) {
                const sourceIndicator = result.source === 'AI' ? '🤖 AI: ' : '💬 ';
                setTimeout(() => addBotMessage(sourceIndicator + result.response), 500);
                return;
            }
        }
    } catch (error) {
        console.log('API chat not available, using local responses:', error);
    }

    // Fallback to local responses
    setTimeout(() => {
        const response = generateBotResponse(message);
        console.log('Generated response:', response); // Debug log
        addBotMessage(response);
    }, 500);
}

function addUserMessage(message) {
    const messageElement = createMessageElement(message, 'user');
    chatMessages.appendChild(messageElement);
    scrollToBottom();
}

function addBotMessage(message) {
    const messageElement = createMessageElement(message, 'bot');
    chatMessages.appendChild(messageElement);
    scrollToBottom();
}

function createMessageElement(message, type) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${type}-message`;
    
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = type === 'bot' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';
    
    const content = document.createElement('div');
    content.className = 'message-content';
    content.innerHTML = `
        <p>${message}</p>
        <span class="message-time">${new Date().toLocaleTimeString()}</span>
    `;
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(content);
    
    return messageDiv;
}

function generateBotResponse(message) {
    const lowerMessage = message.toLowerCase();
    console.log('Processing message:', lowerMessage); // Debug log

    if (lowerMessage.includes('fields') || lowerMessage.includes('validate') || lowerMessage.includes('what') && lowerMessage.includes('check')) {
        return `📋 I validate 6 key fields in XML policy files:

• **Policy Number** - Format and length validation (6-15 characters)
• **Insured Name** - Required field validation (non-empty)
• **Effective Date** - Date format and logic validation
• **Expiration Date** - Date format and business rules (must be after effective)
• **Vehicle VIN** - 17-character format validation (no I, O, Q)
• **Driver License** - Format and length checks (state-specific)

Each field gets a confidence score and detailed validation message!`;
    }
    
    if (lowerMessage.includes('how') || lowerMessage.includes('work') || lowerMessage.includes('process')) {
        return `⚙️ Here's how the validation works:

1. **XML Parsing** - I extract key fields from your ACORD XML format
2. **Business Rules** - Apply insurance industry standards and formats
3. **AI Analysis** - Use machine learning for intelligent validation
4. **Vector Matching** - Compare against known good patterns in database
5. **Confidence Scoring** - Provide reliability scores (0-100%)

The system catches format errors, business rule violations, and data inconsistencies automatically!`;
    }
    
    if (lowerMessage.includes('example') || lowerMessage.includes('sample')) {
        return `📝 Here are some validation examples:

**✅ Valid Examples:**
• Policy Number: "POL2024001234" (proper format)
• VIN: "1HGBH41JXMN109186" (17 characters, valid format)
• Dates: Expiration after effective date

**❌ Invalid Examples:**  
• Policy Number: "BAD" (too short)
• VIN: "TOOSHORT" (not 17 characters)
• Dates: Expiration before effective date

Upload your XML file and I'll show you real validation results!`;
    }
    
    if (lowerMessage.includes('error') || lowerMessage.includes('problem')) {
        return `🔧 Common issues I can help with:

• **File Format** - Make sure it's a valid XML file
• **File Size** - Keep files under 10MB
• **ACORD Format** - I work best with ACORD insurance XML
• **Required Fields** - Some fields are mandatory for validation

What specific issue are you experiencing?`;
    }
    
    if (lowerMessage.includes('confidence') || lowerMessage.includes('score')) {
        return `📊 Confidence scores indicate how certain I am about each validation:

• **90-100%** - Very confident, clear validation result
• **80-89%** - Confident, minor uncertainty exists
• **70-79%** - Moderate confidence, review recommended
• **Below 70%** - Low confidence, manual review needed

Higher scores mean more reliable validation results!`;
    }

    if (lowerMessage.includes('hello') || lowerMessage.includes('hi') || lowerMessage.includes('hey')) {
        return `👋 Hello! I'm your AI XML validation assistant. I can help you:

• Upload and validate XML policy files
• Explain validation results and errors
• Answer questions about field requirements
• Troubleshoot common issues

What would you like to do today?`;
    }

    if (lowerMessage.includes('help') || lowerMessage.includes('assist')) {
        return `🤝 I'm here to help! Here's what I can do:

• **Validate XML Files** - Upload ACORD insurance XML files
• **Explain Results** - Break down validation findings
• **Field Requirements** - Tell you what each field needs
• **Troubleshoot Issues** - Help fix common problems
• **Answer Questions** - About XML validation process

Try asking: "What fields do you validate?" or upload an XML file!`;
    }

    if (lowerMessage.includes('upload') || lowerMessage.includes('file')) {
        return `📁 To upload an XML file:

1. **Drag & Drop** - Drag your XML file onto the upload area above
2. **Click Browse** - Click "Choose File" to select from your computer
3. **File Requirements** - Must be .xml format, under 10MB
4. **ACORD Format** - Works best with ACORD insurance XML files

Once uploaded, click "Validate XML" to start the analysis!`;
    }

    // Default responses
    const defaultResponses = [
        "I'm your AI XML validation assistant! Upload a file or ask me about validation. Try: 'What fields do you validate?'",
        "Hello! I can help with XML policy validation. Ask me about field requirements, upload process, or validation results.",
        "I'm here to help with XML validation! Try asking 'How does validation work?' or upload an XML file to get started.",
        "Welcome! I can explain validation results, help with uploads, or answer questions about XML policy files. What interests you?"
    ];
    
    return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
}

function scrollToBottom() {
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function toggleChat() {
    const chatBody = document.getElementById('chatbotBody');
    const isMinimized = chatBody.style.display === 'none';
    
    chatBody.style.display = isMinimized ? 'flex' : 'none';
    minimizeChat.innerHTML = isMinimized ? '<i class="fas fa-minus"></i>' : '<i class="fas fa-plus"></i>';
}

// Utility functions
function showLoading() {
    loadingOverlay.style.display = 'flex';
}

function hideLoading() {
    loadingOverlay.style.display = 'none';
}
