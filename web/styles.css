/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 40px;
}

.header-content {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
    margin-bottom: 10px;
}

.logo i {
    font-size: 2.5rem;
    color: #667eea;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.subtitle {
    font-size: 1.1rem;
    color: #666;
    font-weight: 400;
}

/* Main Content Layout */
.main-content {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 30px;
    align-items: start;
}

/* Upload Section */
.upload-section {
    grid-column: 1;
}

.upload-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.upload-header {
    text-align: center;
    margin-bottom: 30px;
}

.upload-header i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
}

.upload-header h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-header p {
    color: #666;
    font-size: 1rem;
}

.upload-area {
    border: 3px dashed #ddd;
    border-radius: 15px;
    padding: 60px 30px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.upload-text {
    font-size: 1.2rem;
    color: #666;
    margin-bottom: 10px;
}

.upload-subtext {
    color: #999;
    margin-bottom: 20px;
}

.upload-btn {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.upload-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

/* File Info */
.file-info {
    margin-top: 20px;
}

.file-details {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 15px;
    margin-bottom: 20px;
}

.file-details i {
    font-size: 2rem;
    color: #667eea;
}

.file-text {
    flex: 1;
}

.file-name {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.file-size {
    color: #666;
    font-size: 0.9rem;
}

.remove-file {
    background: #ff6b6b;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.remove-file:hover {
    background: #ff5252;
    transform: scale(1.1);
}

.validate-btn {
    width: 100%;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 15px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.validate-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(76, 175, 80, 0.3);
}

/* Results Section */
.results-section {
    grid-column: 1;
    margin-top: 30px;
}

.results-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.results-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.results-header i {
    font-size: 1.5rem;
    color: #667eea;
}

.results-header h2 {
    font-size: 1.5rem;
    color: #333;
}

/* Chatbot Section */
.chatbot-section {
    grid-column: 2;
    grid-row: 1 / -1;
}

.chatbot-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    height: 600px;
    display: flex;
    flex-direction: column;
    position: sticky;
    top: 20px;
}

.chatbot-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px 20px 0 0;
}

.chatbot-title {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chatbot-title i {
    font-size: 1.5rem;
    color: #667eea;
}

.chatbot-title h2 {
    font-size: 1.3rem;
    color: #333;
}

.status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #4CAF50;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.minimize-btn {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 5px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.minimize-btn:hover {
    background: #f0f0f0;
    color: #333;
}

.chatbot-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    scroll-behavior: smooth;
}

.message {
    display: flex;
    gap: 12px;
    margin-bottom: 20px;
    animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.user-message .message-avatar {
    background: #f0f0f0;
    color: #333;
}

.message-content {
    flex: 1;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 15px;
    position: relative;
}

.bot-message .message-content {
    background: #f8f9fa;
}

.user-message .message-content {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.message-content p {
    margin: 0;
    line-height: 1.5;
}

.message-time {
    font-size: 0.8rem;
    opacity: 0.7;
    margin-top: 8px;
    display: block;
}

.chat-input-container {
    padding: 20px;
    border-top: 1px solid #eee;
}

.chat-input-wrapper {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.chat-input-wrapper input {
    flex: 1;
    padding: 15px;
    border: 2px solid #eee;
    border-radius: 25px;
    outline: none;
    font-size: 1rem;
    transition: all 0.3s ease;
}

.chat-input-wrapper input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.send-btn {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.quick-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.quick-action {
    background: #f8f9fa;
    border: 1px solid #eee;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 5px;
}

.quick-action:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    background: white;
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Results Styles */
.validation-summary {
    padding: 25px;
    border-radius: 15px;
    margin-bottom: 25px;
}

.validation-summary.success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border: 2px solid #28a745;
}

.validation-summary.error {
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
    border: 2px solid #dc3545;
}

.summary-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
}

.status-icon {
    font-size: 2rem;
}

.summary-header h3 {
    font-size: 1.5rem;
    margin: 0;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 20px;
}

.stat {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: #333;
}

.stat-label {
    font-size: 0.9rem;
    color: #666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.policy-data, .field-results {
    margin-bottom: 25px;
}

.policy-data h4, .field-results h4 {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #333;
    font-size: 1.2rem;
}

.data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.data-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.data-label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.data-value {
    color: #666;
    font-family: 'Courier New', monospace;
}

.results-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.result-item {
    padding: 20px;
    border-radius: 15px;
    border: 2px solid;
    transition: all 0.3s ease;
}

.result-item.valid {
    background: #f8fff9;
    border-color: #28a745;
}

.result-item.invalid {
    background: #fff8f8;
    border-color: #dc3545;
}

.result-header {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 10px;
}

.result-icon {
    font-size: 1.2rem;
}

.result-field {
    flex: 1;
    font-weight: 600;
    color: #333;
}

.result-confidence {
    background: #667eea;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.result-message {
    color: #666;
    font-style: italic;
    margin-left: 35px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .chatbot-section {
        grid-column: 1;
        grid-row: auto;
    }

    .chatbot-card {
        position: relative;
        height: 500px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .logo h1 {
        font-size: 2rem;
    }

    .upload-card,
    .results-card,
    .chatbot-card {
        padding: 20px;
    }

    .upload-area {
        padding: 40px 20px;
    }

    .quick-actions {
        justify-content: center;
    }

    .data-grid {
        grid-template-columns: 1fr;
    }

    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}
