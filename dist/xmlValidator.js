"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.XMLPolicyValidator = void 0;
const dotenv = __importStar(require("dotenv"));
const xmlParser_1 = require("./xmlParser");
const vectorStore_1 = require("./vectorStore");
const aiValidator_1 = require("./aiValidator");
dotenv.config();
class XMLPolicyValidator {
    constructor() {
        this.isInitialized = false;
        this.xmlParser = new xmlParser_1.XMLParser();
        const qdrantUrl = process.env.QDRANT_URL || 'http://localhost:6333';
        const qdrantApiKey = process.env.QDRANT_API_KEY || '';
        const collectionName = process.env.QDRANT_COLLECTION_NAME || 'xml_policy_embeddings';
        const groqApiKey = process.env.GROQ_API_KEY || '';
        this.vectorStore = new vectorStore_1.VectorStore(qdrantUrl, qdrantApiKey, collectionName);
        this.aiValidator = new aiValidator_1.AIValidator(groqApiKey, this.vectorStore);
    }
    async initialize() {
        if (this.isInitialized) {
            return;
        }
        try {
            console.log('Initializing XML Policy Validator...');
            await this.vectorStore.initializeCollection();
            this.isInitialized = true;
            console.log('XML Policy Validator initialized successfully');
        }
        catch (error) {
            console.error('Failed to initialize XML Policy Validator:', error);
            throw error;
        }
    }
    async trainWithSampleXML(xmlFilePath) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        try {
            console.log('Training with sample XML...');
            // Parse the sample XML
            const parsedXML = await this.xmlParser.parseXMLFile(xmlFilePath);
            // Create chunks for vector storage
            const chunks = this.xmlParser.createXMLChunks(parsedXML);
            // Store chunks in vector database
            await this.vectorStore.storeChunks(chunks);
            console.log(`Training completed with ${chunks.length} chunks stored`);
        }
        catch (error) {
            console.error('Error during training:', error);
            throw error;
        }
    }
    async validateXMLFile(xmlFilePath) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        try {
            console.log(`Validating XML file: ${xmlFilePath}`);
            // Parse the XML file
            const parsedXML = await this.xmlParser.parseXMLFile(xmlFilePath);
            // Extract policy data
            const policyData = this.xmlParser.extractPolicyData(parsedXML);
            // Get validation fields
            const validationFields = this.xmlParser.getValidationFields(policyData);
            // Perform AI validation
            const validationResults = await this.aiValidator.validateWithBusinessRules(validationFields);
            // Calculate summary
            const summary = this.calculateSummary(validationResults);
            const isValid = summary.invalidFields === 0;
            console.log(`Validation completed. Overall valid: ${isValid}`);
            return {
                isValid,
                policyData,
                validationResults,
                summary
            };
        }
        catch (error) {
            console.error('Error during validation:', error);
            throw error;
        }
    }
    async validateXMLString(xmlContent) {
        if (!this.isInitialized) {
            await this.initialize();
        }
        try {
            console.log('Validating XML string...');
            // Parse the XML string
            const parsedXML = await this.xmlParser.parseXMLString(xmlContent);
            // Extract policy data
            const policyData = this.xmlParser.extractPolicyData(parsedXML);
            // Get validation fields
            const validationFields = this.xmlParser.getValidationFields(policyData);
            // Perform AI validation
            const validationResults = await this.aiValidator.validateWithBusinessRules(validationFields);
            // Calculate summary
            const summary = this.calculateSummary(validationResults);
            const isValid = summary.invalidFields === 0;
            console.log(`Validation completed. Overall valid: ${isValid}`);
            return {
                isValid,
                policyData,
                validationResults,
                summary
            };
        }
        catch (error) {
            console.error('Error during validation:', error);
            throw error;
        }
    }
    calculateSummary(validationResults) {
        const totalFields = validationResults.length;
        const validFields = validationResults.filter(r => r.isValid).length;
        const invalidFields = totalFields - validFields;
        const averageConfidence = totalFields > 0
            ? validationResults.reduce((sum, r) => sum + r.confidence, 0) / totalFields
            : 0;
        return {
            totalFields,
            validFields,
            invalidFields,
            averageConfidence: Math.round(averageConfidence * 100) / 100
        };
    }
    async getVectorStoreInfo() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        return await this.vectorStore.getCollectionInfo();
    }
    async clearTrainingData() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        await this.vectorStore.deleteCollection();
        await this.vectorStore.initializeCollection();
        console.log('Training data cleared');
    }
    formatValidationReport(results) {
        let report = '\n=== XML Policy Validation Report ===\n\n';
        report += `Overall Status: ${results.isValid ? '✅ VALID' : '❌ INVALID'}\n\n`;
        report += '--- Policy Data ---\n';
        Object.entries(results.policyData).forEach(([key, value]) => {
            report += `${key}: ${value}\n`;
        });
        report += '\n--- Validation Results ---\n';
        results.validationResults.forEach(result => {
            const status = result.isValid ? '✅' : '❌';
            const confidence = Math.round(result.confidence * 100);
            report += `${status} ${result.field}: ${result.message} (${confidence}% confidence)\n`;
        });
        report += '\n--- Summary ---\n';
        report += `Total Fields: ${results.summary.totalFields}\n`;
        report += `Valid Fields: ${results.summary.validFields}\n`;
        report += `Invalid Fields: ${results.summary.invalidFields}\n`;
        report += `Average Confidence: ${Math.round(results.summary.averageConfidence * 100)}%\n`;
        return report;
    }
}
exports.XMLPolicyValidator = XMLPolicyValidator;
//# sourceMappingURL=xmlValidator.js.map