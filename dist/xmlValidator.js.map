{"version": 3, "file": "xmlValidator.js", "sourceRoot": "", "sources": ["../src/xmlValidator.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,2CAAwC;AAExC,yDAAsD;AACtD,+CAA4C;AAG5C,MAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,MAAa,kBAAkB;IAM7B;QAFQ,kBAAa,GAAY,KAAK,CAAC;QAGrC,IAAI,CAAC,SAAS,GAAG,IAAI,qBAAS,EAAE,CAAC;QAEjC,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,uBAAuB,CAAC;QACpE,MAAM,YAAY,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,EAAE,CAAC;QACtD,MAAM,cAAc,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,uBAAuB,CAAC;QACrF,MAAM,UAAU,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,EAAE,CAAC;QAElD,iEAAiE;QACjE,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;QAC5E,IAAI,CAAC,WAAW,GAAG,IAAI,mCAAgB,CAAC,SAAS,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;QACjF,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,UAAU,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;IACnE,CAAC;IAED,KAAK,CAAC,UAAU;QACd,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;YACvB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;YACpD,MAAM,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC;YAC9C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;YAC1B,OAAO,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;QAC/D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4CAA4C,EAAE,KAAK,CAAC,CAAC;YACnE,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,WAAmB;QAC1C,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;YAE3C,uBAAuB;YACvB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEjE,mCAAmC;YACnC,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;YAEzD,kCAAkC;YAClC,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAE3C,OAAO,CAAC,GAAG,CAAC,2BAA2B,MAAM,CAAC,MAAM,gBAAgB,CAAC,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;YAC/C,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,WAAmB;QAWvC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,wBAAwB,WAAW,EAAE,CAAC,CAAC;YAEnD,qBAAqB;YACrB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC;YAEjE,sBAAsB;YACtB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAE/D,wBAAwB;YACxB,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAExE,wBAAwB;YACxB,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;YAE7F,oBAAoB;YACpB,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;YAEzD,MAAM,OAAO,GAAG,OAAO,CAAC,aAAa,KAAK,CAAC,CAAC;YAE5C,OAAO,CAAC,GAAG,CAAC,wCAAwC,OAAO,EAAE,CAAC,CAAC;YAE/D,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,OAAO;aACR,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,UAAkB;QAWxC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QAED,IAAI,CAAC;YACH,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;YAExC,uBAAuB;YACvB,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;YAElE,sBAAsB;YACtB,MAAM,UAAU,GAAG,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;YAE/D,wBAAwB;YACxB,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;YAExE,wBAAwB;YACxB,MAAM,iBAAiB,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,yBAAyB,CAAC,gBAAgB,CAAC,CAAC;YAE7F,oBAAoB;YACpB,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;YAEzD,MAAM,OAAO,GAAG,OAAO,CAAC,aAAa,KAAK,CAAC,CAAC;YAE5C,OAAO,CAAC,GAAG,CAAC,wCAAwC,OAAO,EAAE,CAAC,CAAC;YAE/D,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,iBAAiB;gBACjB,OAAO;aACR,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAC;YACjD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAEO,gBAAgB,CAAC,iBAAqC;QAM5D,MAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC;QAC7C,MAAM,WAAW,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC;QACpE,MAAM,aAAa,GAAG,WAAW,GAAG,WAAW,CAAC;QAChD,MAAM,iBAAiB,GAAG,WAAW,GAAG,CAAC;YACvC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,WAAW;YAC3E,CAAC,CAAC,CAAC,CAAC;QAEN,OAAO;YACL,WAAW;YACX,WAAW;YACX,aAAa;YACb,iBAAiB,EAAE,IAAI,CAAC,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,GAAG,GAAG;SAC7D,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QACD,OAAO,MAAM,IAAI,CAAC,WAAW,CAAC,iBAAiB,EAAE,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,iBAAiB;QACrB,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAC;QAC1B,CAAC;QACD,MAAM,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;QAC1C,MAAM,IAAI,CAAC,WAAW,CAAC,oBAAoB,EAAE,CAAC;QAC9C,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACvC,CAAC;IAED,sBAAsB,CAAC,OAKtB;QACC,IAAI,MAAM,GAAG,4CAA4C,CAAC;QAE1D,MAAM,IAAI,mBAAmB,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,MAAM,CAAC;QAE7E,MAAM,IAAI,uBAAuB,CAAC;QAClC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC1D,MAAM,IAAI,GAAG,GAAG,KAAK,KAAK,IAAI,CAAC;QACjC,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,gCAAgC,CAAC;QAC3C,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACzC,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;YAC1C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,UAAU,GAAG,GAAG,CAAC,CAAC;YACvD,MAAM,IAAI,GAAG,MAAM,IAAI,MAAM,CAAC,KAAK,KAAK,MAAM,CAAC,OAAO,KAAK,UAAU,iBAAiB,CAAC;QACzF,CAAC,CAAC,CAAC;QAEH,MAAM,IAAI,qBAAqB,CAAC;QAChC,MAAM,IAAI,iBAAiB,OAAO,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;QAC3D,MAAM,IAAI,iBAAiB,OAAO,CAAC,OAAO,CAAC,WAAW,IAAI,CAAC;QAC3D,MAAM,IAAI,mBAAmB,OAAO,CAAC,OAAO,CAAC,aAAa,IAAI,CAAC;QAC/D,MAAM,IAAI,uBAAuB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,iBAAiB,GAAG,GAAG,CAAC,KAAK,CAAC;QAE1F,OAAO,MAAM,CAAC;IAChB,CAAC;CACF;AAnOD,gDAmOC"}