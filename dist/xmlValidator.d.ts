import { ValidationResult, PolicyData } from './types';
export declare class XMLPolicyValidator {
    private xmlParser;
    private vectorStore;
    private aiValidator;
    private isInitialized;
    constructor();
    initialize(): Promise<void>;
    trainWithSampleXML(xmlFilePath: string): Promise<void>;
    validateXMLFile(xmlFilePath: string): Promise<{
        isValid: boolean;
        policyData: PolicyData;
        validationResults: ValidationResult[];
        summary: {
            totalFields: number;
            validFields: number;
            invalidFields: number;
            averageConfidence: number;
        };
    }>;
    validateXMLString(xmlContent: string): Promise<{
        isValid: boolean;
        policyData: PolicyData;
        validationResults: ValidationResult[];
        summary: {
            totalFields: number;
            validFields: number;
            invalidFields: number;
            averageConfidence: number;
        };
    }>;
    private calculateSummary;
    getVectorStoreInfo(): Promise<any>;
    clearTrainingData(): Promise<void>;
    formatValidationReport(results: {
        isValid: boolean;
        policyData: PolicyData;
        validationResults: ValidationResult[];
        summary: any;
    }): string;
}
//# sourceMappingURL=xmlValidator.d.ts.map