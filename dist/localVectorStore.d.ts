import { XMLChunk } from './types';
export declare class LocalVectorStore {
    private chunks;
    private collectionName;
    constructor(qdrantUrl: string, qdrantApiKey: string, collectionName: string);
    initializeCollection(): Promise<void>;
    private createSimpleEmbedding;
    storeChunks(chunks: XMLChunk[]): Promise<void>;
    searchSimilar(query: string, limit?: number): Promise<any[]>;
    private cosineSimilarity;
    getCollectionInfo(): Promise<any>;
    deleteCollection(): Promise<void>;
}
//# sourceMappingURL=localVectorStore.d.ts.map