"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIValidator = void 0;
const groq_sdk_1 = __importDefault(require("groq-sdk"));
class AIValidator {
    constructor(groqApiKey, vectorStore) {
        this.groqClient = new groq_sdk_1.default({
            apiKey: groqApiKey,
        });
        this.vectorStore = vectorStore;
    }
    async validateFields(fields) {
        const results = [];
        for (const field of fields) {
            try {
                const result = await this.validateSingleField(field);
                results.push(result);
            }
            catch (error) {
                console.error(`Error validating field ${field.name}:`, error);
                results.push({
                    field: field.name,
                    isValid: false,
                    message: `Validation error: ${error}`,
                    confidence: 0
                });
            }
        }
        return results;
    }
    async validateSingleField(field) {
        // Search for similar patterns in the vector database
        const similarChunks = await this.vectorStore.searchSimilar(`${field.name} ${field.value}`, 3);
        // Create context from similar chunks
        const context = similarChunks
            .map((chunk) => chunk.payload?.content || '')
            .join('\n\n');
        // Create validation prompt
        const prompt = this.createValidationPrompt(field, context);
        try {
            const completion = await this.groqClient.chat.completions.create({
                messages: [
                    {
                        role: 'system',
                        content: 'You are an expert XML policy validator. Analyze the provided field and determine if it is valid according to insurance industry standards. Respond with a JSON object containing isValid (boolean), message (string), and confidence (number between 0-1).'
                    },
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                model: 'llama3-8b-8192',
                temperature: 0.1,
                max_tokens: 500,
            });
            const response = completion.choices[0]?.message?.content;
            if (!response) {
                throw new Error('No response from AI model');
            }
            // Parse the AI response
            const aiResult = this.parseAIResponse(response);
            return {
                field: field.name,
                isValid: aiResult.isValid,
                message: aiResult.message,
                confidence: aiResult.confidence
            };
        }
        catch (error) {
            console.error('Error calling Groq API:', error);
            return {
                field: field.name,
                isValid: false,
                message: `AI validation failed: ${error}`,
                confidence: 0
            };
        }
    }
    createValidationPrompt(field, context) {
        return `
Please validate the following insurance policy field:

Field Name: ${field.name}
Field Value: ${field.value}
Field Type: ${field.type}
Required: ${field.required}
Path: ${field.path}

Context from similar policy data:
${context}

Validation Rules:
1. Policy Number: Should be alphanumeric, typically 8-12 characters
2. Insured Name: Should be a valid person or business name, not empty
3. Effective Date: Should be a valid date in YYYY-MM-DD format
4. Expiration Date: Should be a valid date after effective date
5. Vehicle VIN: Should be 17 characters, alphanumeric
6. Driver License: Should follow state format patterns

Please analyze this field and provide your assessment as a JSON object with:
- isValid: boolean indicating if the field is valid
- message: string explaining the validation result
- confidence: number between 0 and 1 indicating your confidence in the assessment

Consider:
- Format correctness
- Business logic validity
- Industry standards
- Data consistency with context
`;
    }
    parseAIResponse(response) {
        try {
            // Try to extract JSON from the response
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                const parsed = JSON.parse(jsonMatch[0]);
                return {
                    isValid: Boolean(parsed.isValid),
                    message: String(parsed.message || 'No message provided'),
                    confidence: Math.max(0, Math.min(1, Number(parsed.confidence || 0)))
                };
            }
            // Fallback parsing if JSON is not found
            const isValidMatch = response.toLowerCase().includes('true') ||
                response.toLowerCase().includes('valid');
            return {
                isValid: isValidMatch,
                message: response.substring(0, 200),
                confidence: 0.5
            };
        }
        catch (error) {
            console.error('Error parsing AI response:', error);
            return {
                isValid: false,
                message: 'Failed to parse AI response',
                confidence: 0
            };
        }
    }
    async validateWithBusinessRules(fields) {
        const results = await this.validateFields(fields);
        // Apply additional business rule validations
        const enhancedResults = results.map(result => {
            const field = fields.find(f => f.name === result.field);
            if (!field)
                return result;
            // Apply specific business rules
            const businessRuleResult = this.applyBusinessRules(field, fields);
            return {
                ...result,
                isValid: result.isValid && businessRuleResult.isValid,
                message: businessRuleResult.isValid ? result.message :
                    `${result.message} | Business Rule: ${businessRuleResult.message}`,
                confidence: Math.min(result.confidence, businessRuleResult.confidence)
            };
        });
        return enhancedResults;
    }
    applyBusinessRules(field, allFields) {
        switch (field.name) {
            case 'Effective Date':
            case 'Expiration Date':
                return this.validateDateLogic(field, allFields);
            case 'Vehicle VIN':
                return this.validateVIN(field);
            case 'Policy Number':
                return this.validatePolicyNumber(field);
            default:
                return { isValid: true, message: 'No specific business rules', confidence: 1.0 };
        }
    }
    validateDateLogic(field, allFields) {
        try {
            const fieldDate = new Date(String(field.value));
            if (isNaN(fieldDate.getTime())) {
                return { isValid: false, message: 'Invalid date format', confidence: 0.9 };
            }
            if (field.name === 'Expiration Date') {
                const effectiveField = allFields.find(f => f.name === 'Effective Date');
                if (effectiveField) {
                    const effectiveDate = new Date(String(effectiveField.value));
                    if (fieldDate <= effectiveDate) {
                        return {
                            isValid: false,
                            message: 'Expiration date must be after effective date',
                            confidence: 0.95
                        };
                    }
                }
            }
            return { isValid: true, message: 'Date validation passed', confidence: 0.9 };
        }
        catch (error) {
            return { isValid: false, message: 'Date validation error', confidence: 0.8 };
        }
    }
    validateVIN(field) {
        const vin = String(field.value).replace(/\s/g, '');
        if (vin.length !== 17) {
            return { isValid: false, message: 'VIN must be 17 characters', confidence: 0.95 };
        }
        if (!/^[A-HJ-NPR-Z0-9]+$/i.test(vin)) {
            return {
                isValid: false,
                message: 'VIN contains invalid characters (I, O, Q not allowed)',
                confidence: 0.9
            };
        }
        return { isValid: true, message: 'VIN format is valid', confidence: 0.9 };
    }
    validatePolicyNumber(field) {
        const policyNum = String(field.value).trim();
        if (policyNum.length < 6 || policyNum.length > 15) {
            return {
                isValid: false,
                message: 'Policy number should be 6-15 characters',
                confidence: 0.8
            };
        }
        if (!/^[A-Z0-9]+$/i.test(policyNum)) {
            return {
                isValid: false,
                message: 'Policy number should contain only letters and numbers',
                confidence: 0.85
            };
        }
        return { isValid: true, message: 'Policy number format is valid', confidence: 0.8 };
    }
}
exports.AIValidator = AIValidator;
//# sourceMappingURL=aiValidator.js.map