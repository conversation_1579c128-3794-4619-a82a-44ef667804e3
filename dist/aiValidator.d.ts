import { ValidationField, ValidationResult } from './types';
import { VectorStore } from './vectorStore';
import { LocalVectorStore } from './localVectorStore';
export declare class AIValidator {
    private groqClient;
    private vectorStore;
    constructor(groqApiKey: string, vectorStore: VectorStore | LocalVectorStore);
    validateFields(fields: ValidationField[]): Promise<ValidationResult[]>;
    private validateSingleField;
    private createValidationPrompt;
    private parseAIResponse;
    validateWithBusinessRules(fields: ValidationField[]): Promise<ValidationResult[]>;
    private applyBusinessRules;
    private validateDateLogic;
    private validateVIN;
    private validatePolicyNumber;
}
//# sourceMappingURL=aiValidator.d.ts.map