"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIValidator = exports.VectorStore = exports.XMLParser = exports.XMLPolicyValidator = void 0;
const xmlValidator_1 = require("./xmlValidator");
const path = __importStar(require("path"));
var xmlValidator_2 = require("./xmlValidator");
Object.defineProperty(exports, "XMLPolicyValidator", { enumerable: true, get: function () { return xmlValidator_2.XMLPolicyValidator; } });
var xmlParser_1 = require("./xmlParser");
Object.defineProperty(exports, "XMLParser", { enumerable: true, get: function () { return xmlParser_1.XMLParser; } });
var vectorStore_1 = require("./vectorStore");
Object.defineProperty(exports, "VectorStore", { enumerable: true, get: function () { return vectorStore_1.VectorStore; } });
var aiValidator_1 = require("./aiValidator");
Object.defineProperty(exports, "AIValidator", { enumerable: true, get: function () { return aiValidator_1.AIValidator; } });
__exportStar(require("./types"), exports);
// Main function for CLI usage
async function main() {
    try {
        const validator = new xmlValidator_1.XMLPolicyValidator();
        // Initialize the validator
        await validator.initialize();
        // Path to the sample XML file
        const sampleXMLPath = path.join(process.cwd(), 'Main Street America_myprefix_SC3.xml');
        console.log('=== AI XML Policy Validator POC ===\n');
        // Train with the sample XML
        console.log('Step 1: Training with sample XML...');
        await validator.trainWithSampleXML(sampleXMLPath);
        // Validate the same XML (in real scenario, this would be a different file)
        console.log('\nStep 2: Validating XML file...');
        const results = await validator.validateXMLFile(sampleXMLPath);
        // Display the results
        const report = validator.formatValidationReport(results);
        console.log(report);
        // Show vector store info
        console.log('\n--- Vector Store Information ---');
        const vectorInfo = await validator.getVectorStoreInfo();
        console.log(`Collection: ${vectorInfo.config?.params?.vectors?.size || 'N/A'} dimensions`);
        console.log(`Points stored: ${vectorInfo.points_count || 0}`);
    }
    catch (error) {
        console.error('Error running XML Policy Validator:', error);
        process.exit(1);
    }
}
// Run main function if this file is executed directly
if (require.main === module) {
    main();
}
//# sourceMappingURL=index.js.map