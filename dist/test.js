"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
const xmlValidator_1 = require("./xmlValidator");
const path = __importStar(require("path"));
const fs = __importStar(require("fs"));
async function runTests() {
    console.log('🧪 Running AI XML Policy Validator Tests\n');
    const validator = new xmlValidator_1.XMLPolicyValidator();
    try {
        // Test 1: Initialize validator
        console.log('Test 1: Initializing validator...');
        await validator.initialize();
        console.log('✅ Validator initialized successfully\n');
        // Test 2: Train with sample XML
        console.log('Test 2: Training with sample XML...');
        const sampleXMLPath = path.join(process.cwd(), 'Main Street America_myprefix_SC3.xml');
        if (!fs.existsSync(sampleXMLPath)) {
            throw new Error(`Sample XML file not found: ${sampleXMLPath}`);
        }
        await validator.trainWithSampleXML(sampleXMLPath);
        console.log('✅ Training completed successfully\n');
        // Test 3: Validate the sample XML
        console.log('Test 3: Validating sample XML...');
        const results = await validator.validateXMLFile(sampleXMLPath);
        console.log('📊 Validation Results:');
        console.log(`- Overall Valid: ${results.isValid ? '✅' : '❌'}`);
        console.log(`- Total Fields: ${results.summary.totalFields}`);
        console.log(`- Valid Fields: ${results.summary.validFields}`);
        console.log(`- Invalid Fields: ${results.summary.invalidFields}`);
        console.log(`- Average Confidence: ${Math.round(results.summary.averageConfidence * 100)}%\n`);
        // Test 4: Display detailed validation results
        console.log('Test 4: Detailed field validation results:');
        results.validationResults.forEach((result, index) => {
            const status = result.isValid ? '✅' : '❌';
            const confidence = Math.round(result.confidence * 100);
            console.log(`${index + 1}. ${status} ${result.field}`);
            console.log(`   Message: ${result.message}`);
            console.log(`   Confidence: ${confidence}%\n`);
        });
        // Test 5: Test with modified XML (create invalid data)
        console.log('Test 5: Testing with invalid XML data...');
        const invalidXMLContent = await createInvalidXMLForTesting();
        const invalidResults = await validator.validateXMLString(invalidXMLContent);
        console.log('📊 Invalid XML Results:');
        console.log(`- Overall Valid: ${invalidResults.isValid ? '✅' : '❌'}`);
        console.log(`- Invalid Fields: ${invalidResults.summary.invalidFields}`);
        if (invalidResults.summary.invalidFields > 0) {
            console.log('✅ Successfully detected invalid fields\n');
        }
        else {
            console.log('⚠️  Warning: Expected to find invalid fields\n');
        }
        // Test 6: Vector store information
        console.log('Test 6: Vector store information...');
        const vectorInfo = await validator.getVectorStoreInfo();
        console.log(`- Collection exists: ✅`);
        console.log(`- Points stored: ${vectorInfo.points_count || 0}`);
        console.log(`- Vector dimensions: ${vectorInfo.config?.params?.vectors?.size || 'N/A'}\n`);
        console.log('🎉 All tests completed successfully!');
    }
    catch (error) {
        console.error('❌ Test failed:', error);
        process.exit(1);
    }
}
async function createInvalidXMLForTesting() {
    // Create a modified version of the XML with invalid data for testing
    return `<ACORD>
  <InsuranceSvcRq>
    <CommlAutoPolicyQuoteInqRq>
      <CommlPolicy>
        <PolicyNumber>INVALID</PolicyNumber>
        <ContractTerm>
          <EffectiveDt>invalid-date</EffectiveDt>
          <ExpirationDt>2020-01-01</ExpirationDt>
        </ContractTerm>
      </CommlPolicy>
      <InsuredOrPrincipal>
        <GeneralPartyInfo>
          <NameInfo>
            <CommlName>
              <CommercialName></CommercialName>
            </CommlName>
          </NameInfo>
        </GeneralPartyInfo>
      </InsuredOrPrincipal>
      <CommlAutoLineBusiness>
        <CommlRateState>
          <CommlVeh>
            <VehIdentificationNumber>INVALID_VIN</VehIdentificationNumber>
          </CommlVeh>
        </CommlRateState>
        <CommlDriver>
          <DriverInfo>
            <DriversLicense>
              <DriversLicenseNumber>INVALID</DriversLicenseNumber>
            </DriversLicense>
          </DriverInfo>
        </CommlDriver>
      </CommlAutoLineBusiness>
    </CommlAutoPolicyQuoteInqRq>
  </InsuranceSvcRq>
</ACORD>`;
}
// Performance test
async function performanceTest() {
    console.log('\n🚀 Running Performance Test...');
    const validator = new xmlValidator_1.XMLPolicyValidator();
    await validator.initialize();
    const sampleXMLPath = path.join(process.cwd(), 'Main Street America_myprefix_SC3.xml');
    // Train once
    await validator.trainWithSampleXML(sampleXMLPath);
    const iterations = 5;
    const startTime = Date.now();
    for (let i = 0; i < iterations; i++) {
        await validator.validateXMLFile(sampleXMLPath);
    }
    const endTime = Date.now();
    const avgTime = (endTime - startTime) / iterations;
    console.log(`⏱️  Average validation time: ${avgTime.toFixed(2)}ms`);
    console.log(`📈 Validations per second: ${(1000 / avgTime).toFixed(2)}`);
}
// Run tests
if (require.main === module) {
    runTests()
        .then(() => performanceTest())
        .catch(console.error);
}
//# sourceMappingURL=test.js.map