{"version": 3, "file": "xmlValidator.d.ts", "sourceRoot": "", "sources": ["../src/xmlValidator.ts"], "names": [], "mappings": "AAIA,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAmB,MAAM,SAAS,CAAC;AAIxE,qBAAa,kBAAkB;IAC7B,OAAO,CAAC,SAAS,CAAY;IAC7B,OAAO,CAAC,WAAW,CAAc;IACjC,OAAO,CAAC,WAAW,CAAc;IACjC,OAAO,CAAC,aAAa,CAAkB;;IAcjC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;IAgB3B,kBAAkB,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAwBtD,eAAe,CAAC,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC;QAClD,OAAO,EAAE,OAAO,CAAC;QACjB,UAAU,EAAE,UAAU,CAAC;QACvB,iBAAiB,EAAE,gBAAgB,EAAE,CAAC;QACtC,OAAO,EAAE;YACP,WAAW,EAAE,MAAM,CAAC;YACpB,WAAW,EAAE,MAAM,CAAC;YACpB,aAAa,EAAE,MAAM,CAAC;YACtB,iBAAiB,EAAE,MAAM,CAAC;SAC3B,CAAC;KACH,CAAC;IAwCI,iBAAiB,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC;QACnD,OAAO,EAAE,OAAO,CAAC;QACjB,UAAU,EAAE,UAAU,CAAC;QACvB,iBAAiB,EAAE,gBAAgB,EAAE,CAAC;QACtC,OAAO,EAAE;YACP,WAAW,EAAE,MAAM,CAAC;YACpB,WAAW,EAAE,MAAM,CAAC;YACpB,aAAa,EAAE,MAAM,CAAC;YACtB,iBAAiB,EAAE,MAAM,CAAC;SAC3B,CAAC;KACH,CAAC;IAwCF,OAAO,CAAC,gBAAgB;IAqBlB,kBAAkB,IAAI,OAAO,CAAC,GAAG,CAAC;IAOlC,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;IASxC,sBAAsB,CAAC,OAAO,EAAE;QAC9B,OAAO,EAAE,OAAO,CAAC;QACjB,UAAU,EAAE,UAAU,CAAC;QACvB,iBAAiB,EAAE,gBAAgB,EAAE,CAAC;QACtC,OAAO,EAAE,GAAG,CAAC;KACd,GAAG,MAAM;CAyBX"}