"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.XMLParser = void 0;
const xml2js = __importStar(require("xml2js"));
const fs = __importStar(require("fs"));
const uuid_1 = require("uuid");
class XMLParser {
    constructor() {
        this.parser = new xml2js.Parser({
            explicitArray: false,
            ignoreAttrs: false,
            mergeAttrs: true
        });
    }
    async parseXMLFile(filePath) {
        try {
            const xmlContent = fs.readFileSync(filePath, 'utf8');
            return await this.parseXMLString(xmlContent);
        }
        catch (error) {
            throw new Error(`Failed to parse XML file: ${error}`);
        }
    }
    async parseXMLString(xmlContent) {
        try {
            return await this.parser.parseStringPromise(xmlContent);
        }
        catch (error) {
            throw new Error(`Failed to parse XML string: ${error}`);
        }
    }
    extractPolicyData(parsedXML) {
        const data = {};
        try {
            // Navigate through the ACORD structure
            const acord = parsedXML.ACORD;
            const insuranceSvcRq = acord?.InsuranceSvcRq;
            const policyQuoteInq = insuranceSvcRq?.CommlAutoPolicyQuoteInqRq;
            // Extract policy number
            const policy = policyQuoteInq?.CommlPolicy;
            if (policy?.PolicyNumber) {
                data.policyNumber = policy.PolicyNumber;
            }
            // Extract insured name
            const insured = policyQuoteInq?.InsuredOrPrincipal;
            const nameInfo = insured?.GeneralPartyInfo?.NameInfo;
            if (nameInfo?.CommlName?.CommercialName) {
                data.insuredName = nameInfo.CommlName.CommercialName;
            }
            else if (nameInfo?.PersonName) {
                const person = nameInfo.PersonName;
                data.insuredName = `${person.GivenName || ''} ${person.Surname || ''}`.trim();
            }
            // Extract effective and expiration dates
            const contractTerm = policy?.ContractTerm;
            if (contractTerm?.EffectiveDt) {
                data.effectiveDate = contractTerm.EffectiveDt;
            }
            if (contractTerm?.ExpirationDt) {
                data.expirationDate = contractTerm.ExpirationDt;
            }
            // Extract vehicle VIN
            const autoLineBusiness = policyQuoteInq?.CommlAutoLineBusiness;
            const vehicle = autoLineBusiness?.CommlRateState?.CommlVeh;
            if (vehicle?.VehIdentificationNumber) {
                data.vehicleVIN = vehicle.VehIdentificationNumber;
            }
            // Extract driver license
            const driver = autoLineBusiness?.CommlDriver;
            if (Array.isArray(driver) && driver.length > 0) {
                data.driverLicense = driver[0]?.DriverInfo?.DriversLicense?.DriversLicenseNumber;
            }
            else if (driver?.DriverInfo?.DriversLicense?.DriversLicenseNumber) {
                data.driverLicense = driver.DriverInfo.DriversLicense.DriversLicenseNumber;
            }
        }
        catch (error) {
            console.warn('Error extracting policy data:', error);
        }
        return data;
    }
    getValidationFields(policyData) {
        const fields = [];
        // Define the 6 key fields for POC validation
        const fieldDefinitions = [
            { key: 'policyNumber', name: 'Policy Number', type: 'string', required: true },
            { key: 'insuredName', name: 'Insured Name', type: 'string', required: true },
            { key: 'effectiveDate', name: 'Effective Date', type: 'date', required: true },
            { key: 'expirationDate', name: 'Expiration Date', type: 'date', required: true },
            { key: 'vehicleVIN', name: 'Vehicle VIN', type: 'string', required: true },
            { key: 'driverLicense', name: 'Driver License', type: 'string', required: false }
        ];
        fieldDefinitions.forEach(def => {
            const value = policyData[def.key];
            if (value !== undefined) {
                fields.push({
                    name: def.name,
                    value: value,
                    path: def.key,
                    required: def.required,
                    type: def.type
                });
            }
        });
        return fields;
    }
    createXMLChunks(parsedXML) {
        const chunks = [];
        try {
            const acord = parsedXML.ACORD;
            // Chunk 1: Policy Information
            const policyInfo = acord?.InsuranceSvcRq?.CommlAutoPolicyQuoteInqRq?.CommlPolicy;
            if (policyInfo) {
                chunks.push({
                    id: (0, uuid_1.v4)(),
                    content: JSON.stringify(policyInfo, null, 2),
                    metadata: {
                        section: 'Policy Information',
                        path: 'ACORD.InsuranceSvcRq.CommlAutoPolicyQuoteInqRq.CommlPolicy',
                        fields: ['PolicyNumber', 'LOBCd', 'EffectiveDt', 'ExpirationDt', 'CurrentTermAmt']
                    }
                });
            }
            // Chunk 2: Insured Information
            const insuredInfo = acord?.InsuranceSvcRq?.CommlAutoPolicyQuoteInqRq?.InsuredOrPrincipal;
            if (insuredInfo) {
                chunks.push({
                    id: (0, uuid_1.v4)(),
                    content: JSON.stringify(insuredInfo, null, 2),
                    metadata: {
                        section: 'Insured Information',
                        path: 'ACORD.InsuranceSvcRq.CommlAutoPolicyQuoteInqRq.InsuredOrPrincipal',
                        fields: ['InsurerId', 'PersonName', 'CommlName', 'Addr', 'PhoneNumber']
                    }
                });
            }
            // Chunk 3: Vehicle Information
            const vehicleInfo = acord?.InsuranceSvcRq?.CommlAutoPolicyQuoteInqRq?.CommlAutoLineBusiness?.CommlRateState?.CommlVeh;
            if (vehicleInfo) {
                chunks.push({
                    id: (0, uuid_1.v4)(),
                    content: JSON.stringify(vehicleInfo, null, 2),
                    metadata: {
                        section: 'Vehicle Information',
                        path: 'ACORD.InsuranceSvcRq.CommlAutoPolicyQuoteInqRq.CommlAutoLineBusiness.CommlRateState.CommlVeh',
                        fields: ['Manufacturer', 'Model', 'ModelYear', 'VehIdentificationNumber', 'Registration']
                    }
                });
            }
        }
        catch (error) {
            console.warn('Error creating XML chunks:', error);
        }
        return chunks;
    }
}
exports.XMLParser = XMLParser;
//# sourceMappingURL=xmlParser.js.map