import { XMLChunk } from './types';
export declare class VectorStore {
    private qdrantClient;
    private openaiClient;
    private collectionName;
    constructor(qdrantUrl: string, qdrantApiKey: string, collectionName: string);
    initializeCollection(): Promise<void>;
    private createSimpleEmbedding;
    storeChunks(chunks: XMLChunk[]): Promise<void>;
    searchSimilar(query: string, limit?: number): Promise<any[]>;
    getCollectionInfo(): Promise<any>;
    deleteCollection(): Promise<void>;
}
//# sourceMappingURL=vectorStore.d.ts.map