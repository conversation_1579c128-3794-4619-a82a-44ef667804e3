{"version": 3, "file": "aiValidator.js", "sourceRoot": "", "sources": ["../src/aiValidator.ts"], "names": [], "mappings": ";;;;;;AAAA,wDAA4B;AAK5B,MAAa,WAAW;IAItB,YAAY,UAAkB,EAAE,WAA2C;QACzE,IAAI,CAAC,UAAU,GAAG,IAAI,kBAAI,CAAC;YACzB,MAAM,EAAE,UAAU;YAClB,+CAA+C;YAC/C,uBAAuB,EAAE,KAAK;SAC/B,CAAC,CAAC;QACH,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IACjC,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAyB;QAC5C,MAAM,OAAO,GAAuB,EAAE,CAAC;QAEvC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE,CAAC;YAC3B,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;gBACrD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACvB,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,0BAA0B,KAAK,CAAC,IAAI,GAAG,EAAE,KAAK,CAAC,CAAC;gBAC9D,OAAO,CAAC,IAAI,CAAC;oBACX,KAAK,EAAE,KAAK,CAAC,IAAI;oBACjB,OAAO,EAAE,KAAK;oBACd,OAAO,EAAE,qBAAqB,KAAK,EAAE;oBACrC,UAAU,EAAE,CAAC;iBACd,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAC,KAAsB;QACtD,qDAAqD;QACrD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,aAAa,CACxD,GAAG,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE,EAC9B,CAAC,CACF,CAAC;QAEF,qCAAqC;QACrC,MAAM,OAAO,GAAG,aAAa;aAC1B,GAAG,CAAC,CAAC,KAAU,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,IAAI,EAAE,CAAC;aACjD,IAAI,CAAC,MAAM,CAAC,CAAC;QAEhB,2BAA2B;QAC3B,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,KAAK,EAAE,OAAO,CAAC,CAAC;QAE3D,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;gBAC/D,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,4PAA4P;qBACtQ;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM;qBAChB;iBACF;gBACD,KAAK,EAAE,gBAAgB;gBACvB,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,GAAG;aAChB,CAAC,CAAC;YAEH,MAAM,QAAQ,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC;YACzD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;YAC/C,CAAC;YAED,wBAAwB;YACxB,MAAM,QAAQ,GAAG,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,CAAC;YAEhD,OAAO;gBACL,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,OAAO,EAAE,QAAQ,CAAC,OAAO;gBACzB,UAAU,EAAE,QAAQ,CAAC,UAAU;aAChC,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;YAChD,OAAO;gBACL,KAAK,EAAE,KAAK,CAAC,IAAI;gBACjB,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yBAAyB,KAAK,EAAE;gBACzC,UAAU,EAAE,CAAC;aACd,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,sBAAsB,CAAC,KAAsB,EAAE,OAAe;QACpE,OAAO;;;cAGG,KAAK,CAAC,IAAI;eACT,KAAK,CAAC,KAAK;cACZ,KAAK,CAAC,IAAI;YACZ,KAAK,CAAC,QAAQ;QAClB,KAAK,CAAC,IAAI;;;EAGhB,OAAO;;;;;;;;;;;;;;;;;;;;CAoBR,CAAC;IACA,CAAC;IAEO,eAAe,CAAC,QAAgB;QACtC,IAAI,CAAC;YACH,wCAAwC;YACxC,MAAM,SAAS,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAChD,IAAI,SAAS,EAAE,CAAC;gBACd,MAAM,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,OAAO;oBACL,OAAO,EAAE,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC;oBAChC,OAAO,EAAE,MAAM,CAAC,MAAM,CAAC,OAAO,IAAI,qBAAqB,CAAC;oBACxD,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC;iBACrE,CAAC;YACJ,CAAC;YAED,wCAAwC;YACxC,MAAM,YAAY,GAAG,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC;gBACxC,QAAQ,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAE7D,OAAO;gBACL,OAAO,EAAE,YAAY;gBACrB,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;gBACnC,UAAU,EAAE,GAAG;aAChB,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,6BAA6B;gBACtC,UAAU,EAAE,CAAC;aACd,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,MAAyB;QACvD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC;QAElD,6CAA6C;QAC7C,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;YAC3C,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC;YACxD,IAAI,CAAC,KAAK;gBAAE,OAAO,MAAM,CAAC;YAE1B,gCAAgC;YAChC,MAAM,kBAAkB,GAAG,IAAI,CAAC,kBAAkB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;YAElE,OAAO;gBACL,GAAG,MAAM;gBACT,OAAO,EAAE,MAAM,CAAC,OAAO,IAAI,kBAAkB,CAAC,OAAO;gBACrD,OAAO,EAAE,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;oBAC9C,GAAG,MAAM,CAAC,OAAO,qBAAqB,kBAAkB,CAAC,OAAO,EAAE;gBAC1E,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,kBAAkB,CAAC,UAAU,CAAC;aACvE,CAAC;QACJ,CAAC,CAAC,CAAC;QAEH,OAAO,eAAe,CAAC;IACzB,CAAC;IAEO,kBAAkB,CAAC,KAAsB,EAAE,SAA4B;QAG7E,QAAQ,KAAK,CAAC,IAAI,EAAE,CAAC;YACnB,KAAK,gBAAgB,CAAC;YACtB,KAAK,iBAAiB;gBACpB,OAAO,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC;YAElD,KAAK,aAAa;gBAChB,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAEjC,KAAK,eAAe;gBAClB,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC;YAE1C;gBACE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,4BAA4B,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;QACrF,CAAC;IACH,CAAC;IAEO,iBAAiB,CAAC,KAAsB,EAAE,SAA4B;QAG5E,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAEhD,IAAI,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC;gBAC/B,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,qBAAqB,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;YAC7E,CAAC;YAED,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE,CAAC;gBACrC,MAAM,cAAc,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,gBAAgB,CAAC,CAAC;gBACxE,IAAI,cAAc,EAAE,CAAC;oBACnB,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;oBAC7D,IAAI,SAAS,IAAI,aAAa,EAAE,CAAC;wBAC/B,OAAO;4BACL,OAAO,EAAE,KAAK;4BACd,OAAO,EAAE,8CAA8C;4BACvD,UAAU,EAAE,IAAI;yBACjB,CAAC;oBACJ,CAAC;gBACH,CAAC;YACH,CAAC;YAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,wBAAwB,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;QAC/E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,uBAAuB,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;QAC/E,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,KAAsB;QAGxC,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAEnD,IAAI,GAAG,CAAC,MAAM,KAAK,EAAE,EAAE,CAAC;YACtB,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,2BAA2B,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC;QACpF,CAAC;QAED,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;YACrC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uDAAuD;gBAChE,UAAU,EAAE,GAAG;aAChB,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,qBAAqB,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IAC5E,CAAC;IAEO,oBAAoB,CAAC,KAAsB;QAGjD,MAAM,SAAS,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;QAE7C,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;YAClD,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,yCAAyC;gBAClD,UAAU,EAAE,GAAG;aAChB,CAAC;QACJ,CAAC;QAED,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;YACpC,OAAO;gBACL,OAAO,EAAE,KAAK;gBACd,OAAO,EAAE,uDAAuD;gBAChE,UAAU,EAAE,IAAI;aACjB,CAAC;QACJ,CAAC;QAED,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,+BAA+B,EAAE,UAAU,EAAE,GAAG,EAAE,CAAC;IACtF,CAAC;CACF;AAlRD,kCAkRC"}