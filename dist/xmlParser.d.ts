import { ValidationField, PolicyData, XMLChunk } from './types';
export declare class XMLParser {
    private parser;
    constructor();
    parseXMLFile(filePath: string): Promise<any>;
    parseXMLString(xmlContent: string): Promise<any>;
    extractPolicyData(parsedXML: any): PolicyData;
    getValidationFields(policyData: PolicyData): ValidationField[];
    createXMLChunks(parsedXML: any): XMLChunk[];
}
//# sourceMappingURL=xmlParser.d.ts.map