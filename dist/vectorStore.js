"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VectorStore = void 0;
const js_client_rest_1 = require("@qdrant/js-client-rest");
const openai_1 = require("openai");
class VectorStore {
    constructor(qdrantUrl, qdrantApi<PERSON>ey, collectionName) {
        this.qdrantClient = new js_client_rest_1.QdrantClient({
            url: qdrantUrl,
            apiKey: qdrant<PERSON>pi<PERSON>ey,
            checkCompatibility: false, // Skip version check for cloud instances
        });
        // Using OpenAI for embeddings (you can switch to other providers)
        this.openaiClient = new openai_1.OpenAI({
            apiKey: process.env.OPENAI_API_KEY || 'dummy-key', // We'll use a simple embedding approach
        });
        this.collectionName = collectionName;
    }
    async initializeCollection() {
        try {
            // Check if collection exists
            const collections = await this.qdrantClient.getCollections();
            const collectionExists = collections.collections.some((col) => col.name === this.collectionName);
            if (!collectionExists) {
                // Create collection with 384 dimensions (for sentence-transformers/all-MiniLM-L6-v2)
                await this.qdrantClient.createCollection(this.collectionName, {
                    vectors: {
                        size: 384,
                        distance: 'Cosine',
                    },
                });
                console.log(`Created collection: ${this.collectionName}`);
            }
            else {
                console.log(`Collection ${this.collectionName} already exists`);
            }
        }
        catch (error) {
            console.error('Error initializing collection:', error);
            throw error;
        }
    }
    // Simple embedding function using text characteristics
    createSimpleEmbedding(text) {
        // This is a simplified embedding approach for the POC
        // In production, you'd use proper embedding models
        const words = text.toLowerCase().split(/\s+/);
        const embedding = new Array(384).fill(0);
        // Create features based on text characteristics
        embedding[0] = text.length / 1000; // Text length feature
        embedding[1] = words.length / 100; // Word count feature
        embedding[2] = (text.match(/\d/g) || []).length / text.length; // Digit ratio
        embedding[3] = (text.match(/[A-Z]/g) || []).length / text.length; // Uppercase ratio
        // Add word-based features
        const commonWords = ['policy', 'insurance', 'vehicle', 'driver', 'coverage', 'date', 'number'];
        commonWords.forEach((word, index) => {
            if (index < 380) {
                embedding[index + 4] = words.filter(w => w.includes(word)).length / words.length;
            }
        });
        // Normalize the embedding
        const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
        return embedding.map(val => magnitude > 0 ? val / magnitude : 0);
    }
    async storeChunks(chunks) {
        try {
            const points = chunks.map(chunk => ({
                id: chunk.id,
                vector: this.createSimpleEmbedding(chunk.content),
                payload: {
                    content: chunk.content,
                    section: chunk.metadata.section,
                    path: chunk.metadata.path,
                    fields: chunk.metadata.fields,
                },
            }));
            await this.qdrantClient.upsert(this.collectionName, {
                wait: true,
                points: points,
            });
            console.log(`Stored ${chunks.length} chunks in vector database`);
        }
        catch (error) {
            console.error('Error storing chunks:', error);
            throw error;
        }
    }
    async searchSimilar(query, limit = 5) {
        try {
            const queryVector = this.createSimpleEmbedding(query);
            const searchResult = await this.qdrantClient.search(this.collectionName, {
                vector: queryVector,
                limit: limit,
                with_payload: true,
            });
            return searchResult;
        }
        catch (error) {
            console.error('Error searching similar chunks:', error);
            throw error;
        }
    }
    async getCollectionInfo() {
        try {
            return await this.qdrantClient.getCollection(this.collectionName);
        }
        catch (error) {
            console.error('Error getting collection info:', error);
            throw error;
        }
    }
    async deleteCollection() {
        try {
            await this.qdrantClient.deleteCollection(this.collectionName);
            console.log(`Deleted collection: ${this.collectionName}`);
        }
        catch (error) {
            console.error('Error deleting collection:', error);
            throw error;
        }
    }
}
exports.VectorStore = VectorStore;
//# sourceMappingURL=vectorStore.js.map