import * as dotenv from 'dotenv';
import { XMLParser } from './xmlParser';
import { VectorStore } from './vectorStore';
import { AIValidator } from './aiValidator';
import { ValidationResult, PolicyData, ValidationField } from './types';

dotenv.config();

export class XMLPolicyValidator {
  private xmlParser: XMLParser;
  private vectorStore: VectorStore;
  private aiValidator: AIValidator;
  private isInitialized: boolean = false;

  constructor() {
    this.xmlParser = new XMLParser();

    const qdrantUrl = process.env.QDRANT_URL || 'http://localhost:6333';
    const qdrantApiKey = process.env.QDRANT_API_KEY || '';
    const collectionName = process.env.QDRANT_COLLECTION_NAME || 'xml_policy_embeddings';
    const groqApiKey = process.env.GROQ_API_KEY || '';

    this.vectorStore = new VectorStore(qdrantUrl, qdrantApiKey, collectionName);
    this.aiValidator = new AIValidator(groqApiKey, this.vectorStore);
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('Initializing XML Policy Validator...');
      await this.vectorStore.initializeCollection();
      this.isInitialized = true;
      console.log('XML Policy Validator initialized successfully');
    } catch (error) {
      console.error('Failed to initialize XML Policy Validator:', error);
      throw error;
    }
  }

  async trainWithSampleXML(xmlFilePath: string): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log('Training with sample XML...');
      
      // Parse the sample XML
      const parsedXML = await this.xmlParser.parseXMLFile(xmlFilePath);
      
      // Create chunks for vector storage
      const chunks = this.xmlParser.createXMLChunks(parsedXML);
      
      // Store chunks in vector database
      await this.vectorStore.storeChunks(chunks);
      
      console.log(`Training completed with ${chunks.length} chunks stored`);
    } catch (error) {
      console.error('Error during training:', error);
      throw error;
    }
  }

  async validateXMLFile(xmlFilePath: string): Promise<{
    isValid: boolean;
    policyData: PolicyData;
    validationResults: ValidationResult[];
    summary: {
      totalFields: number;
      validFields: number;
      invalidFields: number;
      averageConfidence: number;
    };
  }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log(`Validating XML file: ${xmlFilePath}`);
      
      // Parse the XML file
      const parsedXML = await this.xmlParser.parseXMLFile(xmlFilePath);
      
      // Extract policy data
      const policyData = this.xmlParser.extractPolicyData(parsedXML);
      
      // Get validation fields
      const validationFields = this.xmlParser.getValidationFields(policyData);
      
      // Perform AI validation
      const validationResults = await this.aiValidator.validateWithBusinessRules(validationFields);
      
      // Calculate summary
      const summary = this.calculateSummary(validationResults);
      
      const isValid = summary.invalidFields === 0;
      
      console.log(`Validation completed. Overall valid: ${isValid}`);
      
      return {
        isValid,
        policyData,
        validationResults,
        summary
      };
      
    } catch (error) {
      console.error('Error during validation:', error);
      throw error;
    }
  }

  async validateXMLString(xmlContent: string): Promise<{
    isValid: boolean;
    policyData: PolicyData;
    validationResults: ValidationResult[];
    summary: {
      totalFields: number;
      validFields: number;
      invalidFields: number;
      averageConfidence: number;
    };
  }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    try {
      console.log('Validating XML string...');
      
      // Parse the XML string
      const parsedXML = await this.xmlParser.parseXMLString(xmlContent);
      
      // Extract policy data
      const policyData = this.xmlParser.extractPolicyData(parsedXML);
      
      // Get validation fields
      const validationFields = this.xmlParser.getValidationFields(policyData);
      
      // Perform AI validation
      const validationResults = await this.aiValidator.validateWithBusinessRules(validationFields);
      
      // Calculate summary
      const summary = this.calculateSummary(validationResults);
      
      const isValid = summary.invalidFields === 0;
      
      console.log(`Validation completed. Overall valid: ${isValid}`);
      
      return {
        isValid,
        policyData,
        validationResults,
        summary
      };
      
    } catch (error) {
      console.error('Error during validation:', error);
      throw error;
    }
  }

  private calculateSummary(validationResults: ValidationResult[]): {
    totalFields: number;
    validFields: number;
    invalidFields: number;
    averageConfidence: number;
  } {
    const totalFields = validationResults.length;
    const validFields = validationResults.filter(r => r.isValid).length;
    const invalidFields = totalFields - validFields;
    const averageConfidence = totalFields > 0 
      ? validationResults.reduce((sum, r) => sum + r.confidence, 0) / totalFields 
      : 0;

    return {
      totalFields,
      validFields,
      invalidFields,
      averageConfidence: Math.round(averageConfidence * 100) / 100
    };
  }

  async getVectorStoreInfo(): Promise<any> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    return await this.vectorStore.getCollectionInfo();
  }

  async clearTrainingData(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
    await this.vectorStore.deleteCollection();
    await this.vectorStore.initializeCollection();
    console.log('Training data cleared');
  }

  formatValidationReport(results: {
    isValid: boolean;
    policyData: PolicyData;
    validationResults: ValidationResult[];
    summary: any;
  }): string {
    let report = '\n=== XML Policy Validation Report ===\n\n';
    
    report += `Overall Status: ${results.isValid ? '✅ VALID' : '❌ INVALID'}\n\n`;
    
    report += '--- Policy Data ---\n';
    Object.entries(results.policyData).forEach(([key, value]) => {
      report += `${key}: ${value}\n`;
    });
    
    report += '\n--- Validation Results ---\n';
    results.validationResults.forEach(result => {
      const status = result.isValid ? '✅' : '❌';
      const confidence = Math.round(result.confidence * 100);
      report += `${status} ${result.field}: ${result.message} (${confidence}% confidence)\n`;
    });
    
    report += '\n--- Summary ---\n';
    report += `Total Fields: ${results.summary.totalFields}\n`;
    report += `Valid Fields: ${results.summary.validFields}\n`;
    report += `Invalid Fields: ${results.summary.invalidFields}\n`;
    report += `Average Confidence: ${Math.round(results.summary.averageConfidence * 100)}%\n`;
    
    return report;
  }
}
