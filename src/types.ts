export interface ValidationField {
  name: string;
  value: string | number;
  path: string;
  required: boolean;
  type: 'string' | 'number' | 'date' | 'email' | 'phone';
}

export interface ValidationResult {
  field: string;
  isValid: boolean;
  message: string;
  confidence: number;
}

export interface PolicyData {
  policyNumber?: string;
  insuredName?: string;
  effectiveDate?: string;
  expirationDate?: string;
  vehicleVIN?: string;
  driverLicense?: string;
  [key: string]: any;
}

export interface XMLChunk {
  id: string;
  content: string;
  metadata: {
    section: string;
    path: string;
    fields: string[];
  };
}

export interface EmbeddingData {
  id: string;
  vector: number[];
  payload: {
    content: string;
    section: string;
    path: string;
    fields: string[];
  };
}
