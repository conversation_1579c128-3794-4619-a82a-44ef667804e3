import { XMLPolicyValidator } from './xmlValidator';
import * as path from 'path';

export { XMLPolicyValidator } from './xmlValidator';
export { XMLParser } from './xmlParser';
export { VectorStore } from './vectorStore';
export { AIValidator } from './aiValidator';
export * from './types';

// Main function for CLI usage
async function main() {
  try {
    const validator = new XMLPolicyValidator();
    
    // Initialize the validator
    await validator.initialize();
    
    // Path to the sample XML file
    const sampleXMLPath = path.join(process.cwd(), 'Main Street America_myprefix_SC3.xml');
    
    console.log('=== AI XML Policy Validator POC ===\n');
    
    // Train with the sample XML
    console.log('Step 1: Training with sample XML...');
    await validator.trainWithSampleXML(sampleXMLPath);
    
    // Validate the same XML (in real scenario, this would be a different file)
    console.log('\nStep 2: Validating XML file...');
    const results = await validator.validateXMLFile(sampleXMLPath);
    
    // Display the results
    const report = validator.formatValidationReport(results);
    console.log(report);
    
    // Show vector store info
    console.log('\n--- Vector Store Information ---');
    const vectorInfo = await validator.getVectorStoreInfo();
    console.log(`Collection: ${vectorInfo.config?.params?.vectors?.size || 'N/A'} dimensions`);
    console.log(`Points stored: ${vectorInfo.points_count || 0}`);
    
  } catch (error) {
    console.error('Error running XML Policy Validator:', error);
    process.exit(1);
  }
}

// Run main function if this file is executed directly
if (require.main === module) {
  main();
}
