import { XMLChunk, EmbeddingData } from './types';

export class MockVectorStore {
  private chunks: Array<{
    id: string;
    vector: number[];
    payload: {
      content: string;
      section: string;
      path: string;
      fields: string[];
    };
  }> = [];

  private collectionName: string;

  constructor(qdrantUrl: string, qdrantApiKey: string, collectionName: string) {
    this.collectionName = collectionName;
    console.log('🔧 Using Mock Vector Store (no Qdrant connection required)');
  }

  async initializeCollection(): Promise<void> {
    console.log(`✅ Mock collection "${this.collectionName}" initialized`);
  }

  // Simple embedding function using text characteristics
  private createSimpleEmbedding(text: string): number[] {
    // This is a simplified embedding approach for the POC
    const words = text.toLowerCase().split(/\s+/);
    const embedding = new Array(384).fill(0);
    
    // Create features based on text characteristics
    embedding[0] = text.length / 1000; // Text length feature
    embedding[1] = words.length / 100; // Word count feature
    embedding[2] = (text.match(/\d/g) || []).length / text.length; // Digit ratio
    embedding[3] = (text.match(/[A-Z]/g) || []).length / text.length; // Uppercase ratio
    
    // Add word-based features
    const commonWords = ['policy', 'insurance', 'vehicle', 'driver', 'coverage', 'date', 'number'];
    commonWords.forEach((word, index) => {
      if (index < 380) {
        embedding[index + 4] = words.filter(w => w.includes(word)).length / words.length;
      }
    });

    // Normalize the embedding
    const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0));
    return embedding.map(val => magnitude > 0 ? val / magnitude : 0);
  }

  async storeChunks(chunks: XMLChunk[]): Promise<void> {
    const points = chunks.map(chunk => ({
      id: chunk.id,
      vector: this.createSimpleEmbedding(chunk.content),
      payload: {
        content: chunk.content,
        section: chunk.metadata.section,
        path: chunk.metadata.path,
        fields: chunk.metadata.fields,
      },
    }));

    this.chunks.push(...points);
    console.log(`📦 Stored ${chunks.length} chunks in mock vector database (total: ${this.chunks.length})`);
  }

  async searchSimilar(query: string, limit: number = 5): Promise<any[]> {
    const queryVector = this.createSimpleEmbedding(query);
    
    // Calculate cosine similarity for each stored chunk
    const similarities = this.chunks.map(chunk => {
      const similarity = this.cosineSimilarity(queryVector, chunk.vector);
      return {
        id: chunk.id,
        score: similarity,
        payload: chunk.payload
      };
    });

    // Sort by similarity and return top results
    const results = similarities
      .sort((a, b) => b.score - a.score)
      .slice(0, limit);

    console.log(`🔍 Found ${results.length} similar chunks for query: "${query.substring(0, 50)}..."`);
    return results;
  }

  private cosineSimilarity(vecA: number[], vecB: number[]): number {
    const dotProduct = vecA.reduce((sum, a, i) => sum + a * vecB[i], 0);
    const magnitudeA = Math.sqrt(vecA.reduce((sum, a) => sum + a * a, 0));
    const magnitudeB = Math.sqrt(vecB.reduce((sum, b) => sum + b * b, 0));
    
    if (magnitudeA === 0 || magnitudeB === 0) return 0;
    return dotProduct / (magnitudeA * magnitudeB);
  }

  async getCollectionInfo(): Promise<any> {
    return {
      config: {
        params: {
          vectors: {
            size: 384
          }
        }
      },
      points_count: this.chunks.length,
      status: 'green'
    };
  }

  async deleteCollection(): Promise<void> {
    this.chunks = [];
    console.log(`🗑️ Mock collection "${this.collectionName}" cleared`);
  }
}
