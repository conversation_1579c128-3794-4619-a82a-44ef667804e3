import * as xml2js from 'xml2js';
import * as fs from 'fs';
import { ValidationField, PolicyData, XMLChunk } from './types';
import { v4 as uuidv4 } from 'uuid';

export class XMLParser {
  private parser: xml2js.Parser;

  constructor() {
    this.parser = new xml2js.Parser({
      explicitArray: false,
      ignoreAttrs: false,
      mergeAttrs: true
    });
  }

  async parseXMLFile(filePath: string): Promise<any> {
    try {
      const xmlContent = fs.readFileSync(filePath, 'utf8');
      return await this.parseXMLString(xmlContent);
    } catch (error) {
      throw new Error(`Failed to parse XML file: ${error}`);
    }
  }

  async parseXMLString(xmlContent: string): Promise<any> {
    try {
      return await this.parser.parseStringPromise(xmlContent);
    } catch (error) {
      throw new Error(`Failed to parse XML string: ${error}`);
    }
  }

  extractPolicyData(parsedXML: any): PolicyData {
    const data: PolicyData = {};

    try {
      // Navigate through the ACORD structure
      const acord = parsedXML.ACORD;
      const insuranceSvcRq = acord?.InsuranceSvcRq;
      const policyQuoteInq = insuranceSvcRq?.CommlAutoPolicyQuoteInqRq;

      // Extract policy number
      const policy = policyQuoteInq?.CommlPolicy;
      if (policy?.PolicyNumber) {
        data.policyNumber = policy.PolicyNumber;
      }

      // Extract insured name
      const insured = policyQuoteInq?.InsuredOrPrincipal;
      const nameInfo = insured?.GeneralPartyInfo?.NameInfo;
      if (nameInfo?.CommlName?.CommercialName) {
        data.insuredName = nameInfo.CommlName.CommercialName;
      } else if (nameInfo?.PersonName) {
        const person = nameInfo.PersonName;
        data.insuredName = `${person.GivenName || ''} ${person.Surname || ''}`.trim();
      }

      // Extract effective and expiration dates
      const contractTerm = policy?.ContractTerm;
      if (contractTerm?.EffectiveDt) {
        data.effectiveDate = contractTerm.EffectiveDt;
      }
      if (contractTerm?.ExpirationDt) {
        data.expirationDate = contractTerm.ExpirationDt;
      }

      // Extract vehicle VIN
      const autoLineBusiness = policyQuoteInq?.CommlAutoLineBusiness;
      const vehicle = autoLineBusiness?.CommlRateState?.CommlVeh;
      if (vehicle?.VehIdentificationNumber) {
        data.vehicleVIN = vehicle.VehIdentificationNumber;
      }

      // Extract driver license
      const driver = autoLineBusiness?.CommlDriver;
      if (Array.isArray(driver) && driver.length > 0) {
        data.driverLicense = driver[0]?.DriverInfo?.DriversLicense?.DriversLicenseNumber;
      } else if (driver?.DriverInfo?.DriversLicense?.DriversLicenseNumber) {
        data.driverLicense = driver.DriverInfo.DriversLicense.DriversLicenseNumber;
      }

    } catch (error) {
      console.warn('Error extracting policy data:', error);
    }

    return data;
  }

  getValidationFields(policyData: PolicyData): ValidationField[] {
    const fields: ValidationField[] = [];

    // Define the 6 key fields for POC validation
    const fieldDefinitions = [
      { key: 'policyNumber', name: 'Policy Number', type: 'string' as const, required: true },
      { key: 'insuredName', name: 'Insured Name', type: 'string' as const, required: true },
      { key: 'effectiveDate', name: 'Effective Date', type: 'date' as const, required: true },
      { key: 'expirationDate', name: 'Expiration Date', type: 'date' as const, required: true },
      { key: 'vehicleVIN', name: 'Vehicle VIN', type: 'string' as const, required: true },
      { key: 'driverLicense', name: 'Driver License', type: 'string' as const, required: false }
    ];

    fieldDefinitions.forEach(def => {
      const value = policyData[def.key];
      if (value !== undefined) {
        fields.push({
          name: def.name,
          value: value,
          path: def.key,
          required: def.required,
          type: def.type
        });
      }
    });

    return fields;
  }

  createXMLChunks(parsedXML: any): XMLChunk[] {
    const chunks: XMLChunk[] = [];

    try {
      const acord = parsedXML.ACORD;
      
      // Chunk 1: Policy Information
      const policyInfo = acord?.InsuranceSvcRq?.CommlAutoPolicyQuoteInqRq?.CommlPolicy;
      if (policyInfo) {
        chunks.push({
          id: uuidv4(),
          content: JSON.stringify(policyInfo, null, 2),
          metadata: {
            section: 'Policy Information',
            path: 'ACORD.InsuranceSvcRq.CommlAutoPolicyQuoteInqRq.CommlPolicy',
            fields: ['PolicyNumber', 'LOBCd', 'EffectiveDt', 'ExpirationDt', 'CurrentTermAmt']
          }
        });
      }

      // Chunk 2: Insured Information
      const insuredInfo = acord?.InsuranceSvcRq?.CommlAutoPolicyQuoteInqRq?.InsuredOrPrincipal;
      if (insuredInfo) {
        chunks.push({
          id: uuidv4(),
          content: JSON.stringify(insuredInfo, null, 2),
          metadata: {
            section: 'Insured Information',
            path: 'ACORD.InsuranceSvcRq.CommlAutoPolicyQuoteInqRq.InsuredOrPrincipal',
            fields: ['InsurerId', 'PersonName', 'CommlName', 'Addr', 'PhoneNumber']
          }
        });
      }

      // Chunk 3: Vehicle Information
      const vehicleInfo = acord?.InsuranceSvcRq?.CommlAutoPolicyQuoteInqRq?.CommlAutoLineBusiness?.CommlRateState?.CommlVeh;
      if (vehicleInfo) {
        chunks.push({
          id: uuidv4(),
          content: JSON.stringify(vehicleInfo, null, 2),
          metadata: {
            section: 'Vehicle Information',
            path: 'ACORD.InsuranceSvcRq.CommlAutoPolicyQuoteInqRq.CommlAutoLineBusiness.CommlRateState.CommlVeh',
            fields: ['Manufacturer', 'Model', 'ModelYear', 'VehIdentificationNumber', 'Registration']
          }
        });
      }

    } catch (error) {
      console.warn('Error creating XML chunks:', error);
    }

    return chunks;
  }
}
