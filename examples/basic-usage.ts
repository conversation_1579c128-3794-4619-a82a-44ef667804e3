import { XMLPolicyValidator } from '../src/xmlValidator';
import * as path from 'path';

/**
 * Basic usage example of the AI XML Policy Validator
 */
async function basicUsageExample() {
  console.log('🔍 AI XML Policy Validator - Basic Usage Example\n');
  
  // Create a new validator instance
  const validator = new XMLPolicyValidator();
  
  try {
    // Step 1: Initialize the validator
    console.log('Step 1: Initializing validator...');
    await validator.initialize();
    console.log('✅ Validator initialized\n');
    
    // Step 2: Train with a sample XML file
    console.log('Step 2: Training with sample XML...');
    const sampleXMLPath = path.join(process.cwd(), 'Main Street America_myprefix_SC3.xml');
    await validator.trainWithSampleXML(sampleXMLPath);
    console.log('✅ Training completed\n');
    
    // Step 3: Validate an XML file
    console.log('Step 3: Validating XML file...');
    const results = await validator.validateXMLFile(sampleXMLPath);
    
    // Step 4: Display results
    console.log('📋 Validation Results:');
    console.log(`Overall Status: ${results.isValid ? '✅ VALID' : '❌ INVALID'}`);
    console.log(`Total Fields Checked: ${results.summary.totalFields}`);
    console.log(`Valid Fields: ${results.summary.validFields}`);
    console.log(`Invalid Fields: ${results.summary.invalidFields}`);
    console.log(`Average Confidence: ${Math.round(results.summary.averageConfidence * 100)}%\n`);
    
    // Step 5: Show detailed field results
    console.log('📊 Field-by-Field Results:');
    results.validationResults.forEach(result => {
      const status = result.isValid ? '✅' : '❌';
      const confidence = Math.round(result.confidence * 100);
      console.log(`${status} ${result.field}: ${result.message} (${confidence}% confidence)`);
    });
    
    // Step 6: Show extracted policy data
    console.log('\n📄 Extracted Policy Data:');
    Object.entries(results.policyData).forEach(([key, value]) => {
      console.log(`${key}: ${value}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

/**
 * Example of validating XML content from a string
 */
async function stringValidationExample() {
  console.log('\n🔤 String Validation Example\n');
  
  const validator = new XMLPolicyValidator();
  await validator.initialize();
  
  // Sample XML content as string
  const xmlContent = `<ACORD>
    <InsuranceSvcRq>
      <CommlAutoPolicyQuoteInqRq>
        <CommlPolicy>
          <PolicyNumber>TEST123456</PolicyNumber>
          <ContractTerm>
            <EffectiveDt>2024-01-01</EffectiveDt>
            <ExpirationDt>2025-01-01</ExpirationDt>
          </ContractTerm>
        </CommlPolicy>
        <InsuredOrPrincipal>
          <GeneralPartyInfo>
            <NameInfo>
              <CommlName>
                <CommercialName>Test Company Inc</CommercialName>
              </CommlName>
            </NameInfo>
          </GeneralPartyInfo>
        </InsuredOrPrincipal>
        <CommlAutoLineBusiness>
          <CommlRateState>
            <CommlVeh>
              <VehIdentificationNumber>1HGBH41JXMN109186</VehIdentificationNumber>
            </CommlVeh>
          </CommlRateState>
        </CommlAutoLineBusiness>
      </CommlAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
  </ACORD>`;
  
  try {
    const results = await validator.validateXMLString(xmlContent);
    
    console.log('📋 String Validation Results:');
    console.log(`Status: ${results.isValid ? '✅ VALID' : '❌ INVALID'}`);
    console.log(`Fields Validated: ${results.summary.totalFields}`);
    
    // Generate and display formatted report
    const report = validator.formatValidationReport(results);
    console.log(report);
    
  } catch (error) {
    console.error('❌ Error validating XML string:', error);
  }
}

/**
 * Example of batch validation
 */
async function batchValidationExample() {
  console.log('\n📦 Batch Validation Example\n');
  
  const validator = new XMLPolicyValidator();
  await validator.initialize();
  
  // Train with sample data first
  const sampleXMLPath = path.join(process.cwd(), 'Main Street America_myprefix_SC3.xml');
  await validator.trainWithSampleXML(sampleXMLPath);
  
  // Simulate multiple XML files to validate
  const xmlFiles = [
    sampleXMLPath,
    // In a real scenario, you would have multiple different XML files
  ];
  
  console.log(`Validating ${xmlFiles.length} XML files...`);
  
  const batchResults = [];
  
  for (let i = 0; i < xmlFiles.length; i++) {
    const filePath = xmlFiles[i];
    console.log(`\nValidating file ${i + 1}/${xmlFiles.length}: ${path.basename(filePath)}`);
    
    try {
      const result = await validator.validateXMLFile(filePath);
      batchResults.push({
        file: path.basename(filePath),
        isValid: result.isValid,
        summary: result.summary
      });
      
      console.log(`✅ ${result.isValid ? 'VALID' : 'INVALID'} - ${result.summary.validFields}/${result.summary.totalFields} fields valid`);
      
    } catch (error) {
      console.error(`❌ Error validating ${filePath}:`, error);
      batchResults.push({
        file: path.basename(filePath),
        isValid: false,
        error: error.message
      });
    }
  }
  
  // Summary of batch results
  console.log('\n📊 Batch Validation Summary:');
  const validFiles = batchResults.filter(r => r.isValid).length;
  console.log(`Total Files: ${batchResults.length}`);
  console.log(`Valid Files: ${validFiles}`);
  console.log(`Invalid Files: ${batchResults.length - validFiles}`);
}

// Run examples
if (require.main === module) {
  (async () => {
    await basicUsageExample();
    await stringValidationExample();
    await batchValidationExample();
  })().catch(console.error);
}
