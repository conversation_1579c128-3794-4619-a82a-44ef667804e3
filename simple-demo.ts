import { XMLParser } from './src/xmlParser';
import * as path from 'path';

async function runSimpleDemo() {
  console.log('🚀 AI XML Policy Validator - Simple Demo\n');
  console.log('This demo shows XML parsing and business rule validation:\n');
  
  try {
    // Step 1: Parse XML and extract data
    console.log('📄 Step 1: Parsing XML and extracting policy data...');
    const parser = new XMLParser();
    const sampleXMLPath = path.join(process.cwd(), 'Main Street America_myprefix_SC3.xml');
    
    const parsedXML = await parser.parseXMLFile(sampleXMLPath);
    const policyData = parser.extractPolicyData(parsedXML);
    
    console.log('📊 Extracted Policy Data:');
    Object.entries(policyData).forEach(([key, value]) => {
      console.log(`   ${key}: ${value}`);
    });
    console.log();
    
    // Step 2: Get validation fields
    console.log('🔍 Step 2: Preparing fields for validation...');
    const validationFields = parser.getValidationFields(policyData);
    
    console.log(`📋 Found ${validationFields.length} fields to validate:`);
    validationFields.forEach((field, index) => {
      console.log(`   ${index + 1}. ${field.name}: ${field.value} (${field.type}, required: ${field.required})`);
    });
    console.log();
    
    // Step 3: Apply business rule validation (without AI for now)
    console.log('⚖️  Step 3: Applying business rule validation...');
    
    const businessRuleResults = validationFields.map(field => {
      return applyBusinessRules(field, validationFields);
    });
    
    console.log('📈 Business Rule Validation Results:');
    businessRuleResults.forEach((result, index) => {
      const status = result.isValid ? '✅' : '❌';
      console.log(`   ${status} ${validationFields[index].name}: ${result.message}`);
    });
    console.log();
    
    // Step 4: Test with invalid data
    console.log('🧪 Step 4: Testing with invalid XML data...');
    const invalidXMLContent = createInvalidXMLForTesting();
    const invalidParsedXML = await parser.parseXMLString(invalidXMLContent);
    const invalidPolicyData = parser.extractPolicyData(invalidParsedXML);
    const invalidFields = parser.getValidationFields(invalidPolicyData);
    
    console.log('📊 Invalid XML Data:');
    Object.entries(invalidPolicyData).forEach(([key, value]) => {
      console.log(`   ${key}: ${value || 'EMPTY'}`);
    });
    console.log();
    
    const invalidResults = invalidFields.map(field => {
      return applyBusinessRules(field, invalidFields);
    });
    
    console.log('🚨 Invalid Data Validation Results:');
    invalidResults.forEach((result, index) => {
      const status = result.isValid ? '✅' : '❌';
      console.log(`   ${status} ${invalidFields[index].name}: ${result.message}`);
    });
    console.log();
    
    // Step 5: Summary
    const validCount = businessRuleResults.filter(r => r.isValid).length;
    const invalidCount = invalidResults.filter(r => !r.isValid).length;
    
    console.log('📊 SUMMARY:');
    console.log(`   ✅ Valid XML: ${validCount}/${businessRuleResults.length} fields passed`);
    console.log(`   ❌ Invalid XML: ${invalidCount}/${invalidResults.length} fields failed (as expected)`);
    console.log();
    
    console.log('🎉 Demo completed successfully!');
    console.log('\n📝 What this demo showed:');
    console.log('   ✅ XML parsing and field extraction from ACORD format');
    console.log('   ✅ Business rule validation (VIN format, date logic, etc.)');
    console.log('   ✅ Detection of invalid data');
    console.log('   ✅ Comprehensive field analysis');
    console.log('\n💡 Note: AI validation with Groq requires SSL certificate setup.');
    console.log('   The business rule validation is working perfectly!');
    
  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

function applyBusinessRules(field: any, allFields: any[]): { isValid: boolean; message: string } {
  switch (field.name) {
    case 'Policy Number':
      return validatePolicyNumber(field);
    
    case 'Insured Name':
      return validateInsuredName(field);
    
    case 'Effective Date':
    case 'Expiration Date':
      return validateDateLogic(field, allFields);
    
    case 'Vehicle VIN':
      return validateVIN(field);
    
    case 'Driver License':
      return validateDriverLicense(field);
    
    default:
      return { isValid: true, message: 'No specific validation rules' };
  }
}

function validatePolicyNumber(field: any): { isValid: boolean; message: string } {
  const policyNum = String(field.value).trim();
  
  if (!policyNum) {
    return { isValid: false, message: 'Policy number is required' };
  }
  
  if (policyNum.length < 6 || policyNum.length > 15) {
    return { isValid: false, message: 'Policy number should be 6-15 characters' };
  }
  
  if (!/^[A-Z0-9]+$/i.test(policyNum)) {
    return { isValid: false, message: 'Policy number should contain only letters and numbers' };
  }
  
  return { isValid: true, message: 'Valid policy number format' };
}

function validateInsuredName(field: any): { isValid: boolean; message: string } {
  const name = String(field.value).trim();
  
  if (!name) {
    return { isValid: false, message: 'Insured name is required' };
  }
  
  if (name.length < 2) {
    return { isValid: false, message: 'Insured name too short' };
  }
  
  return { isValid: true, message: 'Valid insured name' };
}

function validateDateLogic(field: any, allFields: any[]): { isValid: boolean; message: string } {
  try {
    const fieldDate = new Date(String(field.value));
    
    if (isNaN(fieldDate.getTime())) {
      return { isValid: false, message: 'Invalid date format' };
    }

    if (field.name === 'Expiration Date') {
      const effectiveField = allFields.find(f => f.name === 'Effective Date');
      if (effectiveField) {
        const effectiveDate = new Date(String(effectiveField.value));
        if (fieldDate <= effectiveDate) {
          return { isValid: false, message: 'Expiration date must be after effective date' };
        }
      }
    }

    return { isValid: true, message: 'Valid date format and logic' };
  } catch (error) {
    return { isValid: false, message: 'Date validation error' };
  }
}

function validateVIN(field: any): { isValid: boolean; message: string } {
  const vin = String(field.value).replace(/\s/g, '');
  
  if (!vin) {
    return { isValid: false, message: 'VIN is required' };
  }
  
  if (vin.length !== 17) {
    return { isValid: false, message: 'VIN must be exactly 17 characters' };
  }

  if (!/^[A-HJ-NPR-Z0-9]+$/i.test(vin)) {
    return { isValid: false, message: 'VIN contains invalid characters (I, O, Q not allowed)' };
  }

  return { isValid: true, message: 'Valid VIN format' };
}

function validateDriverLicense(field: any): { isValid: boolean; message: string } {
  const license = String(field.value).trim();
  
  if (!license) {
    return { isValid: false, message: 'Driver license is required' };
  }
  
  if (license.length < 5) {
    return { isValid: false, message: 'Driver license too short' };
  }
  
  return { isValid: true, message: 'Valid driver license format' };
}

function createInvalidXMLForTesting(): string {
  return `<ACORD>
    <InsuranceSvcRq>
      <CommlAutoPolicyQuoteInqRq>
        <CommlPolicy>
          <PolicyNumber>BAD</PolicyNumber>
          <ContractTerm>
            <EffectiveDt>invalid-date</EffectiveDt>
            <ExpirationDt>2020-01-01</ExpirationDt>
          </ContractTerm>
        </CommlPolicy>
        <InsuredOrPrincipal>
          <GeneralPartyInfo>
            <NameInfo>
              <CommlName>
                <CommercialName></CommercialName>
              </CommlName>
            </NameInfo>
          </GeneralPartyInfo>
        </InsuredOrPrincipal>
        <CommlAutoLineBusiness>
          <CommlRateState>
            <CommlVeh>
              <VehIdentificationNumber>TOOSHORT</VehIdentificationNumber>
            </CommlVeh>
          </CommlRateState>
          <CommlDriver>
            <DriverInfo>
              <DriversLicense>
                <DriversLicenseNumber>BAD</DriversLicenseNumber>
              </DriversLicense>
            </DriverInfo>
          </CommlDriver>
        </CommlAutoLineBusiness>
      </CommlAutoPolicyQuoteInqRq>
    </InsuranceSvcRq>
  </ACORD>`;
}

// Run the demo
if (require.main === module) {
  runSimpleDemo();
}
