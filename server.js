// Temporary fix for SSL certificate issues in development
// Remove this line in production!
if (process.env.NODE_ENV !== 'production') {
    process.env["NODE_TLS_REJECT_UNAUTHORIZED"] = 0;
    console.log('⚠️  SSL verification disabled for development');
}

const express = require('express');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { XMLPolicyValidator } = require('./dist/xmlValidator');

const app = express();
const PORT = process.env.PORT || 3000;

// Configure multer for file uploads
const upload = multer({
    dest: 'uploads/',
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: (req, file, cb) => {
        if (file.originalname.toLowerCase().endsWith('.xml')) {
            cb(null, true);
        } else {
            cb(new Error('Only XML files are allowed'), false);
        }
    }
});

// Middleware
app.use(express.json());
app.use(express.static('web'));

// Initialize validator
let validator = null;

async function initializeValidator() {
    try {
        validator = new XMLPolicyValidator();
        await validator.initialize();
        console.log('✅ XML Policy Validator initialized');
    } catch (error) {
        console.error('❌ Failed to initialize validator:', error);
    }
}

// Routes
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'web', 'index.html'));
});

// API endpoint for XML validation
app.post('/api/validate', upload.single('xmlFile'), async (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                error: 'No file uploaded'
            });
        }

        console.log(`📁 Validating file: ${req.file.originalname}`);

        // Read the uploaded file
        const filePath = req.file.path;
        const xmlContent = fs.readFileSync(filePath, 'utf8');

        let results;
        
        if (validator) {
            // Use the actual validator
            try {
                results = await validator.validateXMLString(xmlContent);
                console.log('✅ Validation completed successfully');
            } catch (validationError) {
                console.warn('⚠️ Validator error, using fallback:', validationError.message);
                results = await fallbackValidation(xmlContent);
            }
        } else {
            // Fallback validation
            console.log('⚠️ Using fallback validation');
            results = await fallbackValidation(xmlContent);
        }

        // Clean up uploaded file
        fs.unlinkSync(filePath);

        res.json({
            success: true,
            data: results
        });

    } catch (error) {
        console.error('❌ Validation error:', error);
        
        // Clean up file if it exists
        if (req.file && fs.existsSync(req.file.path)) {
            fs.unlinkSync(req.file.path);
        }

        res.status(500).json({
            success: false,
            error: 'Validation failed: ' + error.message
        });
    }
});

// Fallback validation function
async function fallbackValidation(xmlContent) {
    const { XMLParser } = require('./dist/xmlParser');
    
    try {
        const parser = new XMLParser();
        const parsedXML = await parser.parseXMLString(xmlContent);
        const policyData = parser.extractPolicyData(parsedXML);
        const validationFields = parser.getValidationFields(policyData);
        
        // Apply business rules validation
        const validationResults = validationFields.map(field => {
            const businessRuleResult = applyBusinessRules(field, validationFields);
            return {
                field: field.name,
                isValid: businessRuleResult.isValid,
                message: businessRuleResult.message,
                confidence: businessRuleResult.isValid ? 0.85 : 0.90 // High confidence for business rules
            };
        });

        // Calculate summary
        const summary = {
            totalFields: validationResults.length,
            validFields: validationResults.filter(r => r.isValid).length,
            invalidFields: validationResults.filter(r => !r.isValid).length,
            averageConfidence: validationResults.reduce((sum, r) => sum + r.confidence, 0) / validationResults.length
        };

        const isValid = summary.invalidFields === 0;

        return {
            isValid,
            policyData,
            validationResults,
            summary
        };

    } catch (error) {
        throw new Error('Failed to parse XML: ' + error.message);
    }
}

// Business rules validation functions
function applyBusinessRules(field, allFields) {
    switch (field.name) {
        case 'Policy Number':
            return validatePolicyNumber(field);
        case 'Insured Name':
            return validateInsuredName(field);
        case 'Effective Date':
        case 'Expiration Date':
            return validateDateLogic(field, allFields);
        case 'Vehicle VIN':
            return validateVIN(field);
        case 'Driver License':
            return validateDriverLicense(field);
        default:
            return { isValid: true, message: 'No specific validation rules' };
    }
}

function validatePolicyNumber(field) {
    const policyNum = String(field.value).trim();
    
    if (!policyNum) {
        return { isValid: false, message: 'Policy number is required' };
    }
    
    if (policyNum.length < 6 || policyNum.length > 15) {
        return { isValid: false, message: 'Policy number should be 6-15 characters' };
    }
    
    if (!/^[A-Z0-9]+$/i.test(policyNum)) {
        return { isValid: false, message: 'Policy number should contain only letters and numbers' };
    }
    
    return { isValid: true, message: 'Valid policy number format' };
}

function validateInsuredName(field) {
    const name = String(field.value).trim();
    
    if (!name) {
        return { isValid: false, message: 'Insured name is required' };
    }
    
    if (name.length < 2) {
        return { isValid: false, message: 'Insured name too short' };
    }
    
    return { isValid: true, message: 'Valid insured name' };
}

function validateDateLogic(field, allFields) {
    try {
        const fieldDate = new Date(String(field.value));
        
        if (isNaN(fieldDate.getTime())) {
            return { isValid: false, message: 'Invalid date format' };
        }

        if (field.name === 'Expiration Date') {
            const effectiveField = allFields.find(f => f.name === 'Effective Date');
            if (effectiveField) {
                const effectiveDate = new Date(String(effectiveField.value));
                if (fieldDate <= effectiveDate) {
                    return { isValid: false, message: 'Expiration date must be after effective date' };
                }
            }
        }

        return { isValid: true, message: 'Valid date format and logic' };
    } catch (error) {
        return { isValid: false, message: 'Date validation error' };
    }
}

function validateVIN(field) {
    const vin = String(field.value).replace(/\s/g, '');
    
    if (!vin) {
        return { isValid: false, message: 'VIN is required' };
    }
    
    if (vin.length !== 17) {
        return { isValid: false, message: 'VIN must be exactly 17 characters' };
    }

    if (!/^[A-HJ-NPR-Z0-9]+$/i.test(vin)) {
        return { isValid: false, message: 'VIN contains invalid characters (I, O, Q not allowed)' };
    }

    return { isValid: true, message: 'Valid VIN format' };
}

function validateDriverLicense(field) {
    const license = String(field.value).trim();
    
    if (!license || license === '[object Object]') {
        return { isValid: false, message: 'Driver license is required' };
    }
    
    if (license.length < 5) {
        return { isValid: false, message: 'Driver license too short' };
    }
    
    return { isValid: true, message: 'Valid driver license format' };
}

// Chatbot API endpoint
app.post('/api/chat', (req, res) => {
    const { message } = req.body;
    
    if (!message) {
        return res.status(400).json({
            success: false,
            error: 'Message is required'
        });
    }

    // Generate bot response (same logic as frontend)
    const response = generateBotResponse(message);
    
    res.json({
        success: true,
        response: response
    });
});

function generateBotResponse(message) {
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('fields') || lowerMessage.includes('validate')) {
        return `📋 I validate 6 key fields in XML policy files:
        
• **Policy Number** - Format and length validation
• **Insured Name** - Required field validation  
• **Effective Date** - Date format and logic
• **Expiration Date** - Date format and business rules
• **Vehicle VIN** - 17-character format validation
• **Driver License** - Format and length checks

Each field gets a confidence score and detailed validation message!`;
    }
    
    if (lowerMessage.includes('how') || lowerMessage.includes('work')) {
        return `⚙️ Here's how the validation works:

1. **XML Parsing** - I extract key fields from your ACORD XML
2. **Business Rules** - Apply insurance industry standards
3. **AI Analysis** - Use machine learning for intelligent validation
4. **Vector Matching** - Compare against known good patterns
5. **Confidence Scoring** - Provide reliability scores (0-100%)

The system catches format errors, business rule violations, and data inconsistencies!`;
    }
    
    // Add more responses as needed
    return "I'm here to help with XML policy validation! Upload a file or ask me about the validation process.";
}

// Error handling middleware
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                error: 'File too large. Maximum size is 10MB.'
            });
        }
    }
    
    res.status(500).json({
        success: false,
        error: error.message
    });
});

// Start server
async function startServer() {
    await initializeValidator();
    
    app.listen(PORT, () => {
        console.log(`🚀 AI XML Policy Validator Server running on http://localhost:${PORT}`);
        console.log(`📁 Upload XML files and chat with the AI assistant!`);
    });
}

startServer().catch(console.error);
