# AI XML Policy Validator

A TypeScript-based AI-powered XML policy validator that uses Groq AI and Qdrant vector database to validate insurance policy XML files. This is a Proof of Concept (POC) that demonstrates intelligent validation of ACORD XML format insurance policies.

## Features

- 🤖 **AI-Powered Validation**: Uses Groq's LLaMA model for intelligent field validation
- 🔍 **Vector Database**: Leverages Qdrant for storing and retrieving XML patterns
- 📋 **ACORD XML Support**: Specifically designed for ACORD insurance XML format
- ⚡ **TypeScript**: Full TypeScript support with type safety
- 🧪 **Comprehensive Testing**: Includes test suite and examples
- 📊 **Detailed Reporting**: Provides confidence scores and detailed validation messages

## Architecture

1. **XML Parsing**: Parses ACORD XML files and extracts key policy fields
2. **Vector Storage**: Chunks XML content and stores embeddings in Qdrant
3. **AI Validation**: Uses Groq AI to validate fields against learned patterns
4. **Business Rules**: Applies additional business logic validation
5. **Reporting**: Generates comprehensive validation reports

## Validated Fields (POC)

The current POC validates 6 key fields:
- Policy Number
- Insured Name
- Effective Date
- Expiration Date
- Vehicle VIN
- Driver License Number

## Prerequisites

- Node.js 18+ 
- TypeScript
- Groq API key
- Qdrant instance (cloud or local)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd AI-XML-Policy-Validator
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Edit `.env` with your API keys:
```env
GROQ_API_KEY=your_groq_api_key_here
QDRANT_URL=https://your-cluster-url.qdrant.tech:6333
QDRANT_API_KEY=your_qdrant_api_key_here
QDRANT_COLLECTION_NAME=xml_policy_embeddings
```

4. Build the project:
```bash
npm run build
```

## Usage

### Basic Usage

```typescript
import { XMLPolicyValidator } from './src/xmlValidator';

const validator = new XMLPolicyValidator();

// Initialize
await validator.initialize();

// Train with sample XML
await validator.trainWithSampleXML('sample.xml');

// Validate XML file
const results = await validator.validateXMLFile('policy.xml');

console.log(`Valid: ${results.isValid}`);
console.log(`Confidence: ${results.summary.averageConfidence}`);
```

### Command Line Usage

```bash
# Run the main validator
npm run dev

# Run tests
npm test

# Run examples
npx ts-node examples/basic-usage.ts
```

### API Reference

#### XMLPolicyValidator

Main class for XML validation.

**Methods:**
- `initialize()`: Initialize the validator and vector database
- `trainWithSampleXML(filePath)`: Train with a sample XML file
- `validateXMLFile(filePath)`: Validate an XML file
- `validateXMLString(xmlContent)`: Validate XML content from string
- `formatValidationReport(results)`: Format results into a readable report

#### Validation Results

```typescript
interface ValidationResult {
  field: string;
  isValid: boolean;
  message: string;
  confidence: number; // 0-1
}
```

## Examples

### File Validation
```typescript
const results = await validator.validateXMLFile('policy.xml');
if (results.isValid) {
  console.log('✅ Policy is valid');
} else {
  console.log('❌ Policy has validation errors');
  results.validationResults.forEach(result => {
    if (!result.isValid) {
      console.log(`- ${result.field}: ${result.message}`);
    }
  });
}
```

### String Validation
```typescript
const xmlContent = `<ACORD>...</ACORD>`;
const results = await validator.validateXMLString(xmlContent);
```

### Batch Processing
```typescript
const files = ['policy1.xml', 'policy2.xml', 'policy3.xml'];
for (const file of files) {
  const results = await validator.validateXMLFile(file);
  console.log(`${file}: ${results.isValid ? 'VALID' : 'INVALID'}`);
}
```

## Testing

Run the test suite:
```bash
npm test
```

The test suite includes:
- Initialization tests
- Training tests
- Validation tests with valid data
- Validation tests with invalid data
- Performance tests
- Vector database tests

## Configuration

### Environment Variables

- `GROQ_API_KEY`: Your Groq API key for AI validation
- `QDRANT_URL`: Qdrant instance URL
- `QDRANT_API_KEY`: Qdrant API key
- `QDRANT_COLLECTION_NAME`: Collection name for storing embeddings

### Validation Rules

The validator applies both AI-based and rule-based validation:

**AI Validation:**
- Uses Groq's LLaMA model to assess field validity
- Considers context from similar policy data
- Provides confidence scores

**Business Rules:**
- Policy Number: 6-15 alphanumeric characters
- VIN: 17 characters, specific format validation
- Dates: Valid date format and logical consistency
- Names: Non-empty, reasonable format

## Performance

- Average validation time: ~500-1000ms per policy
- Supports concurrent validation
- Vector database provides fast similarity search
- Configurable confidence thresholds

## Limitations (POC)

- Limited to 6 validation fields
- Simplified embedding approach
- Basic business rules
- Single XML format (ACORD)
- No real-time validation UI

## Future Enhancements

- Web interface for file uploads
- More comprehensive field validation
- Support for additional XML formats
- Real-time validation API
- Advanced ML models for validation
- Audit trail and logging
- Integration with insurance systems

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For questions or issues, please create an issue in the repository.
