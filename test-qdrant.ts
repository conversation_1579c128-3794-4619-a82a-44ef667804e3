import { QdrantClient } from '@qdrant/js-client-rest';
import * as dotenv from 'dotenv';

dotenv.config();

async function testQdrantConnection() {
  console.log('🔍 Testing Qdrant Connection...\n');
  
  const qdrantUrl = process.env.QDRANT_URL;
  const qdrantApiKey = process.env.QDRANT_API_KEY;
  
  console.log(`URL: ${qdrantUrl}`);
  console.log(`API Key: ${qdrantApiKey ? qdrantApiKey.substring(0, 20) + '...' : 'Not set'}\n`);
  
  try {
    // Test different URL formats
    const urlsToTest = [
      qdrantUrl,
      qdrantUrl?.replace(':6333', ''),
      'https://fcfd106d-5f3e-4dd3-a759-71050588aa1a.us-west-1-0.aws.cloud.qdrant.io',
      'https://fcfd106d-5f3e-4dd3-a759-71050588aa1a.us-west-1-0.aws.cloud.qdrant.io:6333'
    ];
    
    for (const url of urlsToTest) {
      if (!url) continue;
      
      console.log(`🔗 Testing URL: ${url}`);
      
      try {
        const client = new QdrantClient({
          url: url,
          apiKey: qdrantApiKey,
        });
        
        // Test connection with a simple call
        const collections = await client.getCollections();
        console.log(`✅ Success! Found ${collections.collections.length} collections`);
        
        // Test creating a collection
        const testCollectionName = 'test_connection';
        try {
          await client.createCollection(testCollectionName, {
            vectors: {
              size: 384,
              distance: 'Cosine',
            },
          });
          console.log(`✅ Successfully created test collection`);
          
          // Clean up
          await client.deleteCollection(testCollectionName);
          console.log(`✅ Successfully deleted test collection`);
          
        } catch (createError) {
          console.log(`⚠️  Collection creation test failed: ${createError}`);
        }
        
        console.log(`\n🎉 Qdrant connection successful with URL: ${url}\n`);
        return url;
        
      } catch (error) {
        console.log(`❌ Failed: ${error instanceof Error ? error.message : String(error)}\n`);
      }
    }
    
    console.log('❌ All connection attempts failed');
    return null;
    
  } catch (error) {
    console.error('❌ Connection test failed:', error);
    return null;
  }
}

if (require.main === module) {
  testQdrantConnection();
}
