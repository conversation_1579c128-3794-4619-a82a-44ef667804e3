<?xml version="1.0" encoding="UTF-8"?>
<ACORD>
  <BookTransferMeta>
    <creation>
      <type>xmlExtraction</type>
      <origin>TestSystem</origin>
    </creation>
  </BookTransferMeta>
  <SignonRq>
    <SignonPswd>
      <CustId>
        <SPName>TestServices.com</SPName>
        <CustPermId/>
        <CustLoginId><EMAIL></CustLoginId>
      </CustId>
      <CustPswd>
        <EncryptionTypeCd>NONE</EncryptionTypeCd>
        <Pswd/>
      </CustPswd>
    </SignonPswd>
    <ClientDt>2024-01-15 10:30 AM</ClientDt>
    <CustLangPref>en-US</CustLangPref>
    <ClientApp>
      <Org>Test Insurance</Org>
      <n>PolicyValidator</n>
      <Version>V1.0.0</Version>
    </ClientApp>
  </SignonRq>
  <InsuranceSvcRq BypassScrubbing="SkipNaming">
    <RqUID>12345678-1234-5678-9ABC-123456789012</RqUID>
    <CommlAutoPolicyQuoteInqRq>
      <RqUID>*************-8765-CBA9-************</RqUID>
      <TransactionRequestDt>2024-01-15</TransactionRequestDt>
      <TransactionEffectiveDt>2024-02-01</TransactionEffectiveDt>
      <CurCd>USD</CurCd>
      <Producer>
        <ProducerInfo>
          <ContractNumber>12345678</ContractNumber>
          <ProducerRoleCd>Agency</ProducerRoleCd>
        </ProducerInfo>
      </Producer>
      <InsuredOrPrincipal>
        <ItemIdInfo>
          <InsurerId>TEST123456</InsurerId>
          <OtherIdentifier>
            <OtherIdTypeCd>Insured</OtherIdTypeCd>
            <OtherId>TESTCLIENT</OtherId>
          </OtherIdentifier>
        </ItemIdInfo>
        <GeneralPartyInfo>
          <NameInfo>
            <PersonName>
              <Surname>Johnson</Surname>
              <GivenName>Michael</GivenName>
            </PersonName>
            <CommlName>
              <CommercialName>Johnson Michael</CommercialName>
            </CommlName>
            <LegalEntityCd>CP</LegalEntityCd>
          </NameInfo>
          <Addr>
            <AddrTypeCd>MailingAddress</AddrTypeCd>
            <Addr1>123 Main Street</Addr1>
            <City>Springfield</City>
            <StateProvCd>IL</StateProvCd>
            <PostalCode>62701</PostalCode>
          </Addr>
          <Communications>
            <PhoneInfo>
              <PhoneTypeCd>Phone</PhoneTypeCd>
              <CommunicationUseCd>Day</CommunicationUseCd>
              <PhoneNumber>******-123-4567</PhoneNumber>
            </PhoneInfo>
          </Communications>
        </GeneralPartyInfo>
        <InsuredOrPrincipalInfo>
          <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
          <BusinessInfo>
            <SICCd>8712</SICCd>
            <NAICSCd>541310</NAICSCd>
            <NumEmployees>3</NumEmployees>
          </BusinessInfo>
        </InsuredOrPrincipalInfo>
      </InsuredOrPrincipal>
      <CommlPolicy id="PolicyLevel_TEST">
        <PolicyNumber>POL2024001234</PolicyNumber>
        <LOBCd>AUTOB</LOBCd>
        <LOBSubCd>NPO</LOBSubCd>
        <NAICCd>12345</NAICCd>
        <ControllingStateProvCd>IL</ControllingStateProvCd>
        <ContractTerm>
          <EffectiveDt>2024-02-01</EffectiveDt>
          <ExpirationDt>2025-02-01</ExpirationDt>
          <DurationPeriod>
            <NumUnits>12</NumUnits>
            <UnitMeasurementCd>MON</UnitMeasurementCd>
          </DurationPeriod>
        </ContractTerm>
        <BillingMethodCd>CPB</BillingMethodCd>
        <CurrentTermAmt>
          <Amt>1850.00</Amt>
        </CurrentTermAmt>
        <LanguageCd>E</LanguageCd>
        <RateEffectiveDt>2024-02-01</RateEffectiveDt>
        <PaymentOption>
          <PaymentPlanCd>OT</PaymentPlanCd>
        </PaymentOption>
      </CommlPolicy>
      <Location id="L1_TEST">
        <ItemIdInfo>
          <AgencyId>0001</AgencyId>
        </ItemIdInfo>
        <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>123 Main Street</Addr1>
          <City>Springfield</City>
          <StateProvCd>IL</StateProvCd>
          <PostalCode>62701</PostalCode>
        </Addr>
      </Location>
      <CommlAutoLineBusiness>
        <LOBCd>AUTOB</LOBCd>
        <LOBSubCd>NPO</LOBSubCd>
        <NAICCd>12345</NAICCd>
        <CurrentTermAmt>
          <Amt>1850.00</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>1850.00</Amt>
        </NetChangeAmt>
        <RateEffectiveDt>2024-02-01</RateEffectiveDt>
        <CommlRateState>
          <StateProvCd>IL</StateProvCd>
          <CommlVeh LocationRef="L1_TEST" id="V1_TEST">
            <ItemIdInfo>
              <InsurerId>0001</InsurerId>
            </ItemIdInfo>
            <Manufacturer>HONDA</Manufacturer>
            <Model>CIVIC</Model>
            <ModelYear>2020</ModelYear>
            <VehTypeCd>PP</VehTypeCd>
            <Registration>
              <RegistrationId>ABC123</RegistrationId>
              <StateProvCd>IL</StateProvCd>
            </Registration>
            <CostNewAmt>
              <Amt>25000</Amt>
            </CostNewAmt>
            <FullTermAmt>
              <Amt>1850.00</Amt>
            </FullTermAmt>
            <RegistrationStateProvCd>IL</RegistrationStateProvCd>
            <TerritoryCd>001</TerritoryCd>
            <VehIdentificationNumber>1HGBH41JXMN109186</VehIdentificationNumber>
            <VehSymbolCd>05</VehSymbolCd>
          </CommlVeh>
        </CommlRateState>
        <CommlDriver id="D1_TEST">
          <ItemIdInfo>
            <InsurerId>0001</InsurerId>
          </ItemIdInfo>
          <GeneralPartyInfo>
            <NameInfo>
              <PersonName>
                <Surname>Johnson</Surname>
                <GivenName>Michael</GivenName>
              </PersonName>
              <CommlName>
                <CommercialName>Johnson Michael</CommercialName>
              </CommlName>
            </NameInfo>
          </GeneralPartyInfo>
          <DriverInfo>
            <PersonInfo>
              <GenderCd>M</GenderCd>
              <BirthDt>1985-03-15</BirthDt>
            </PersonInfo>
            <DriversLicense>
              <DriversLicenseNumber>J123456789012</DriversLicenseNumber>
              <StateProvCd>IL</StateProvCd>
            </DriversLicense>
            <License>
              <LicenseTypeCd>Driver</LicenseTypeCd>
              <LicensePermitNumber>J123456789012</LicensePermitNumber>
              <StateProvCd>IL</StateProvCd>
            </License>
          </DriverInfo>
        </CommlDriver>
      </CommlAutoLineBusiness>
    </CommlAutoPolicyQuoteInqRq>
  </InsuranceSvcRq>
</ACORD>
