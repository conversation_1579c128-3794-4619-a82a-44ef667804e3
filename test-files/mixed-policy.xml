<?xml version="1.0" encoding="UTF-8"?>
<ACORD>
  <BookTransferMeta>
    <creation>
      <type>xmlExtraction</type>
      <origin>TestSystem</origin>
    </creation>
  </BookTransferMeta>
  <SignonRq>
    <SignonPswd>
      <CustId>
        <SPName>TestServices.com</SPName>
        <CustPermId/>
        <CustLoginId><EMAIL></CustLoginId>
      </CustId>
    </SignonPswd>
    <ClientDt>2024-01-15 10:30 AM</ClientDt>
    <CustLangPref>en-US</CustLangPref>
  </SignonRq>
  <InsuranceSvcRq BypassScrubbing="SkipNaming">
    <RqUID>12345678-1234-5678-9ABC-123456789012</RqUID>
    <CommlAutoPolicyQuoteInqRq>
      <RqUID>*************-8765-CBA9-************</RqUID>
      <TransactionRequestDt>2024-01-15</TransactionRequestDt>
      <TransactionEffectiveDt>2024-02-01</TransactionEffectiveDt>
      <CurCd>USD</CurCd>
      <Producer>
        <ProducerInfo>
          <ContractNumber>12345678</ContractNumber>
          <ProducerRoleCd>Agency</ProducerRoleCd>
        </ProducerInfo>
      </Producer>
      <InsuredOrPrincipal>
        <ItemIdInfo>
          <InsurerId>TEST123456</InsurerId>
          <OtherIdentifier>
            <OtherIdTypeCd>Insured</OtherIdTypeCd>
            <OtherId>TESTCLIENT</OtherId>
          </OtherIdentifier>
        </ItemIdInfo>
        <GeneralPartyInfo>
          <NameInfo>
            <PersonName>
              <Surname>Smith</Surname>
              <GivenName>Jane</GivenName>
            </PersonName>
            <CommlName>
              <CommercialName>Smith Jane</CommercialName>
            </CommlName>
            <LegalEntityCd>CP</LegalEntityCd>
          </NameInfo>
          <Addr>
            <AddrTypeCd>MailingAddress</AddrTypeCd>
            <Addr1>456 Oak Avenue</Addr1>
            <City>Chicago</City>
            <StateProvCd>IL</StateProvCd>
            <PostalCode>60601</PostalCode>
          </Addr>
          <Communications>
            <PhoneInfo>
              <PhoneTypeCd>Phone</PhoneTypeCd>
              <CommunicationUseCd>Day</CommunicationUseCd>
              <PhoneNumber>******-555-9876</PhoneNumber>
            </PhoneInfo>
          </Communications>
        </GeneralPartyInfo>
        <InsuredOrPrincipalInfo>
          <InsuredOrPrincipalRoleCd>Insured</InsuredOrPrincipalRoleCd>
          <BusinessInfo>
            <SICCd>8712</SICCd>
            <NAICSCd>541310</NAICSCd>
            <NumEmployees>5</NumEmployees>
          </BusinessInfo>
        </InsuredOrPrincipalInfo>
      </InsuredOrPrincipal>
      <CommlPolicy id="PolicyLevel_MIXED">
        <PolicyNumber>POL2024567890</PolicyNumber>
        <LOBCd>AUTOB</LOBCd>
        <LOBSubCd>NPO</LOBSubCd>
        <NAICCd>12345</NAICCd>
        <ControllingStateProvCd>IL</ControllingStateProvCd>
        <ContractTerm>
          <EffectiveDt>2024-03-01</EffectiveDt>
          <ExpirationDt>2024-01-01</ExpirationDt>
          <DurationPeriod>
            <NumUnits>12</NumUnits>
            <UnitMeasurementCd>MON</UnitMeasurementCd>
          </DurationPeriod>
        </ContractTerm>
        <BillingMethodCd>CPB</BillingMethodCd>
        <CurrentTermAmt>
          <Amt>2200.00</Amt>
        </CurrentTermAmt>
        <LanguageCd>E</LanguageCd>
        <RateEffectiveDt>2024-03-01</RateEffectiveDt>
      </CommlPolicy>
      <Location id="L1_MIXED">
        <ItemIdInfo>
          <AgencyId>0001</AgencyId>
        </ItemIdInfo>
        <Addr>
          <AddrTypeCd>MailingAddress</AddrTypeCd>
          <Addr1>456 Oak Avenue</Addr1>
          <City>Chicago</City>
          <StateProvCd>IL</StateProvCd>
          <PostalCode>60601</PostalCode>
        </Addr>
      </Location>
      <CommlAutoLineBusiness>
        <LOBCd>AUTOB</LOBCd>
        <LOBSubCd>NPO</LOBSubCd>
        <NAICCd>12345</NAICCd>
        <CurrentTermAmt>
          <Amt>2200.00</Amt>
        </CurrentTermAmt>
        <NetChangeAmt>
          <Amt>2200.00</Amt>
        </NetChangeAmt>
        <RateEffectiveDt>2024-03-01</RateEffectiveDt>
        <CommlRateState>
          <StateProvCd>IL</StateProvCd>
          <CommlVeh LocationRef="L1_MIXED" id="V1_MIXED">
            <ItemIdInfo>
              <InsurerId>0001</InsurerId>
            </ItemIdInfo>
            <Manufacturer>TOYOTA</Manufacturer>
            <Model>CAMRY</Model>
            <ModelYear>2022</ModelYear>
            <VehTypeCd>PP</VehTypeCd>
            <Registration>
              <RegistrationId>XYZ789</RegistrationId>
              <StateProvCd>IL</StateProvCd>
            </Registration>
            <CostNewAmt>
              <Amt>28000</Amt>
            </CostNewAmt>
            <FullTermAmt>
              <Amt>2200.00</Amt>
            </FullTermAmt>
            <RegistrationStateProvCd>IL</RegistrationStateProvCd>
            <TerritoryCd>002</TerritoryCd>
            <VehIdentificationNumber>4T1BF1FK5CU123456</VehIdentificationNumber>
            <VehSymbolCd>07</VehSymbolCd>
          </CommlVeh>
        </CommlRateState>
        <CommlDriver id="D1_MIXED">
          <ItemIdInfo>
            <InsurerId>0001</InsurerId>
          </ItemIdInfo>
          <GeneralPartyInfo>
            <NameInfo>
              <PersonName>
                <Surname>Smith</Surname>
                <GivenName>Jane</GivenName>
              </PersonName>
              <CommlName>
                <CommercialName>Smith Jane</CommercialName>
              </CommlName>
            </NameInfo>
          </GeneralPartyInfo>
          <DriverInfo>
            <PersonInfo>
              <GenderCd>F</GenderCd>
              <BirthDt>1990-07-22</BirthDt>
            </PersonInfo>
            <DriversLicense>
              <DriversLicenseNumber>S987654321098</DriversLicenseNumber>
              <StateProvCd>IL</StateProvCd>
            </DriversLicense>
            <License>
              <LicenseTypeCd>Driver</LicenseTypeCd>
              <LicensePermitNumber>S987654321098</LicensePermitNumber>
              <StateProvCd>IL</StateProvCd>
            </License>
          </DriverInfo>
        </CommlDriver>
      </CommlAutoLineBusiness>
    </CommlAutoPolicyQuoteInqRq>
  </InsuranceSvcRq>
</ACORD>
