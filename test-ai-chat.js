// Test script to verify AI chat functionality
const fetch = require('node-fetch');

async function testAIChat() {
    console.log('🧪 Testing AI Chat Integration...\n');
    
    const testMessages = [
        "Hello, can you help me?",
        "What fields do you validate in XML files?",
        "How does the validation process work?",
        "What should I do if my VIN is invalid?"
    ];
    
    for (let i = 0; i < testMessages.length; i++) {
        const message = testMessages[i];
        console.log(`\n${i + 1}. Testing: "${message}"`);
        
        try {
            const response = await fetch('http://localhost:3000/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });
            
            if (response.ok) {
                const result = await response.json();
                console.log(`✅ Response Source: ${result.source || 'unknown'}`);
                console.log(`📝 Response: ${result.response.substring(0, 100)}...`);
                
                if (result.source === 'AI') {
                    console.log('🤖 AI MODEL WORKING!');
                } else {
                    console.log('💬 Using local fallback');
                }
            } else {
                console.log(`❌ HTTP Error: ${response.status}`);
            }
            
        } catch (error) {
            console.log(`❌ Request failed: ${error.message}`);
        }
        
        // Wait between requests
        await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n🎯 Test completed!');
    console.log('If you see "AI MODEL WORKING!" above, the Groq integration is successful.');
    console.log('If you see "Using local fallback", there may still be connection issues.');
}

// Run the test
testAIChat().catch(console.error);
